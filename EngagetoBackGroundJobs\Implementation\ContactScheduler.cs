﻿using EngagetoBackGroundJobs.Interfaces;
using EngagetoContracts.ContactContracts;
using EngagetoContracts.Services;
using EngagetoContracts.Workflow;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoDapper.Data.Interfaces.ILogInterfaces;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.ContactDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.Utilities;
using EngagetoEntities.Validations;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PhoneNumbers;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace EngagetoBackGroundJobs.Implementation
{
    public class ContactScheduler : IContactScheduler
    {
        private readonly IBlobStorageService _blobStorageService;
        private readonly ApplicationDbContext _dbContext;
        private readonly IGenericRepository _genericRepository;
        private readonly ILogHistoryService _logHistoryService;
        private readonly IUserIdentityService _userIdentityService;
        private readonly INodeWorkflowEngineService _nodeWorkflowEngineService;
        private readonly ILogger<ContactScheduler> _logger;


        public ContactScheduler(
            IBlobStorageService blobStorageService,
            ApplicationDbContext applicationDbContext,
            IGenericRepository genericRepository,
            ILogHistoryService logHistoryService,
            IUserIdentityService userIdentityService,
            INodeWorkflowEngineService nodeWorkflowEngineService,
            ILogger<ContactScheduler> logger
            )
        {
            _blobStorageService = blobStorageService;
            _genericRepository = genericRepository;
            _dbContext = applicationDbContext;
            _logHistoryService = logHistoryService;
            _userIdentityService = userIdentityService;
            _nodeWorkflowEngineService = nodeWorkflowEngineService;
            _logger = logger;
        }
        public async Task CustomerBulkUploadHandler(Guid trackerId, string BusinessId, Guid CurrentUserId, string Type)
        {
            _logger.LogInformation("Starting customer bulk upload handler for trackerId: {TrackerId}, BusinessId: {BusinessId}, UserId: {UserId}, Type: {Type}",
                trackerId, BusinessId, CurrentUserId, Type);

            var contactImportTracker = await _dbContext.ContactImportTrackers.FirstOrDefaultAsync(x => x.Id == trackerId);

            if (contactImportTracker != null)
            {
                try
                {
                    contactImportTracker.status = UploadStatus.Started;
                    contactImportTracker.UpdatedBy = CurrentUserId;
                    contactImportTracker.CreatedBy = CurrentUserId; ;

                    _dbContext.ContactImportTrackers.Update(contactImportTracker);
                    await _dbContext.SaveChangesAsync();
                    List<Contacts> existingCustomer = new();

                    _logger.LogInformation("Attempting to download file from S3 with key: {S3BucketKey}", contactImportTracker.S3BucketKey);
                    Stream fileStream = await _blobStorageService.GetObjectAsync("dev-engageto", contactImportTracker.S3BucketKey);


                    DataTable dataTable = new();
                    List<InvalidContactDto> invalids = new();
                    if (contactImportTracker?.S3BucketKey?.Split('.')?.LastOrDefault() == "csv")
                    {
                        using MemoryStream memoryStream = new();
                        fileStream.CopyTo(memoryStream);
                        dataTable = ExcelProcessorcs.CSVToDataTable(memoryStream);
                    }
                    else
                    {
                        dataTable = ExcelProcessorcs.ConvertExcelToDataTable(fileStream, contactImportTracker?.SheetName);
                    }
                    int totalRows = dataTable.Rows.Count;
                    for (int i = totalRows - 1; i >= 0; i--)
                    {
                        var row = dataTable.Rows[i];
                        if (row.ItemArray.All(i => string.IsNullOrEmpty(i.ToString())))
                        {
                            row.Delete();
                            totalRows--;
                        }
                    }
                    if (dataTable.Rows.Count <= 0)
                    {
                        throw new Exception("Excel sheet is empty. Please fill some data in the excel sheet template.");
                    }

                    List<Tags> newTags = new();
                    List<string> tagNamesList = dataTable.AsEnumerable()
                           .Select(row => row["Tags"]?.ToString())
                           .Where(name => !string.IsNullOrWhiteSpace(name))
                           .SelectMany(name => SplitTags(name))
                           .Select(name => name.ToLower().Trim())
                           .Distinct()
                           .ToList();

                    List<Tags> existingTags = await _dbContext.Tags
                        .Where(t => t.BusinessId == Guid.Parse(BusinessId) && tagNamesList.Contains(t.Tag.ToLower().Trim()))
                        .ToListAsync();
                    Dictionary<string, Tags> existingTagMap = existingTags
                                                             .DistinctBy(t => t.Tag.ToLower().Trim())
                                                             .ToDictionary(t => t.Tag.ToLower().Trim());


                    dataTable.AsEnumerable().ToList().ForEach(row =>
                    {
                        var tagNamesString = row["Tags"]?.ToString();
                        var listTagNames = SplitTags(tagNamesString);
                        List<Tags> customerTags = new();

                        listTagNames.ForEach(tagName =>
                        {
                            var normalizedTagName = tagName.ToLower().Trim();

                            if (existingTagMap.TryGetValue(normalizedTagName, out var existingTag))
                            {
                                customerTags.Add(existingTag);
                            }
                            else if (!newTags.Any(t => t.Tag.ToLower().Trim() == normalizedTagName))
                            {
                                var newTag = new Tags
                                {
                                    Tag = normalizedTagName,
                                    CreatedBy = CurrentUserId.ToString(),
                                    UpdatedBy = CurrentUserId.ToString(),
                                    CreatedAt = DateTime.UtcNow,
                                    UpdatedAt = DateTime.UtcNow,
                                    BusinessId = Guid.Parse(BusinessId),
                                    UserId = CurrentUserId
                                };
                                newTags.Add(newTag);
                                customerTags.Add(newTag);
                            }
                        });
                    });

                    if (newTags.Any())
                    {
                        await _dbContext.Tags.AddRangeAsync(newTags);
                        await _dbContext.SaveChangesAsync();
                        newTags.ForEach(tag => existingTagMap[tag.Tag.ToLower().Trim()] = tag);
                    }
                    List<Contacts> contacts = ConvertToCustomer(dataTable, existingTagMap.Values.ToList(), BusinessId, CurrentUserId);
                    contacts = contacts.DistinctBy(i => i.Contact).ToList();

                    foreach (Contacts contact in contacts)
                    {
                        if (!string.IsNullOrWhiteSpace(contact.Contact))
                        {
                            try
                            {

                                var (contactNumber, isValid) = PhoneNumberValidator.ValidatePhoneNumber(contact.CountryCode, contact.Contact);
                                if (string.IsNullOrWhiteSpace(contactNumber))
                                {
                                    var invalidCustomer = contact.Adapt<InvalidContactDto>();
                                    invalidCustomer.Errors = "Invalid ContactNo";
                                    invalidCustomer.Source = SourceType.Excel.ToString();
                                    invalids.Add(invalidCustomer);
                                }
                                else
                                {
                                    string mobileNumber = Regex.Replace(contactNumber, "[^0-9]", "");
                                    string defaultRegion = string.Empty;
                                    PhoneNumberUtil phoneUtil = PhoneNumberUtil.GetInstance();

                                    if (mobileNumber.Length > 6 && mobileNumber.Length < 20)
                                    {
                                        PhoneNumber number = phoneUtil.Parse("+" + mobileNumber, null);
                                        defaultRegion = phoneUtil.GetRegionCodeForNumber(number);
                                        string countryCode = phoneUtil.GetCountryCodeForRegion(defaultRegion).ToString();
                                        countryCode = Regex.Replace(countryCode, "[^0-9]", "");
                                        if (mobileNumber.StartsWith(countryCode))
                                        {
                                            mobileNumber = mobileNumber.Substring(countryCode.Length);
                                        }
                                    }
                                    contact.Contact = mobileNumber;
                                }
                            }
                            catch (Exception ex)
                            {
                                var invalidCustomer = contact.Adapt<InvalidContactDto>();
                                invalidCustomer.Errors = "Invalid ContactNo";
                                invalidCustomer.Source = SourceType.Excel.ToString();
                                invalids.Add(invalidCustomer);
                            }
                        }
                        else
                        {
                            var invalidCustomer = contact.Adapt<InvalidContactDto>();
                            invalidCustomer.Errors = "Invalid ContactNo";
                            invalidCustomer.Errors = "Invalid ContactNo";
                            invalidCustomer.Source = SourceType.Excel.ToString();
                            invalids.Add(invalidCustomer);
                        }
                    }
                    var distinctCount = contacts.Count();
                    var contactNos = contacts.SelectMany(i => new[] { i.Contact })
                              .Where(i => !string.IsNullOrWhiteSpace(i)).ToList();


                    foreach (var contactNo in contactNos)
                    {
                        string mobileNumber = Regex.Replace(contactNo, "[^0-9]", "");
                        string defaultRegion = string.Empty;
                        PhoneNumberUtil phoneUtil = PhoneNumberUtil.GetInstance();

                        if (contactNo.StartsWith("+") && contactNo.Length > 6 && contactNo.Length < 20)
                        {
                            PhoneNumber number = phoneUtil.Parse("+" + contactNo, null);
                            defaultRegion = phoneUtil.GetRegionCodeForNumber(number);
                            string countryCode = phoneUtil.GetCountryCodeForRegion(defaultRegion).ToString();
                            countryCode = Regex.Replace(countryCode, "[^0-9]", "");
                            if (mobileNumber.StartsWith(countryCode))
                            {
                                mobileNumber = mobileNumber.Substring(countryCode.Length);
                            }
                        }


                        var custmerForNumber = await _dbContext.Contacts.Where(i => i.Contact == mobileNumber && i.BusinessId.ToString() == BusinessId && i.IsActive).ToListAsync();

                        if (custmerForNumber != null && custmerForNumber.Any())
                        {
                            existingCustomer.AddRange(custmerForNumber);
                        }
                    }

                    var existingContactNos = existingCustomer.SelectMany(i => new[] { i.Contact })
                                        .Where(i => !string.IsNullOrWhiteSpace(i)).ToList();

                    foreach (Contacts contact in contacts)
                    {
                        if (existingContactNos.Any(i => !string.IsNullOrWhiteSpace(contact.Contact)))
                        {
                            var invalidCustomer = contact.Adapt<InvalidContactDto>();
                            invalidCustomer.Errors = "Duplicate Customer";
                            invalidCustomer.Source = SourceType.Excel.ToString();

                            var formatNumber = contact.Contact.Replace("+", "").Replace(" ", "").Trim();
                            var duplicateCustomer = existingCustomer.FirstOrDefault(i => i.Contact == formatNumber);
                            if (duplicateCustomer != null)
                            {
                                invalids.Add(invalidCustomer);
                            }
                        }
                    }
                    ;
                    contacts.RemoveAll(i => invalids.Select(i => i.Contact).Contains(i.Contact));
                    contacts.RemoveAll(i => existingContactNos.Select(i => i).Contains(i.Contact));
                    contactImportTracker.status = UploadStatus.InProgress;
                    contactImportTracker.TotalCount = totalRows;
                    contactImportTracker.DistinctCount = distinctCount;
                    contactImportTracker.UpdatedBy = CurrentUserId;
                    contactImportTracker.CreatedBy = CurrentUserId;


                    if (invalids.Any())
                    {

                        contactImportTracker.DuplicateCount = invalids.Where(i => i.Errors == "Duplicate Customer").Count();
                        contactImportTracker.InvalidCount = invalids.Where(i => i.Errors == "Invalid ContactNo" || i.Errors == "Invalid Name" || i.Errors == "contact number and name are empty").Count();

                        byte[] bytes = ExcelProcessorcs.CreateExcelData(invalids).ToArray();
                        string fileName = $"InvalidCustomers-{DateTime.Now:yyyy_MM_dd-HH_mm_ss}.xlsx";
                        string folder = "Customer";

                        var key = await _blobStorageService.UploadObjectAsync("dev-engageto", folder, fileName, bytes) ?? string.Empty;
                        var presignedUrl = $"https://dev-engageto.s3.ap-south-1.amazonaws.com/" + key;
                        contactImportTracker.InvalidDataS3BucketKey = presignedUrl;


                    }
                    _dbContext.ContactImportTrackers.Update(contactImportTracker);
                    await _dbContext.SaveChangesAsync();

                    _logger.LogInformation("Processing {ContactCount} contacts for bulk upload", contacts.Count);
                    await AddNewContactsAsync(contacts);


                    contactImportTracker.status = UploadStatus.Completed;
                    contactImportTracker.TotalUploadedCount = contacts?.Count ?? 0;
                    contactImportTracker.UpdatedBy = CurrentUserId;
                    contactImportTracker.CreatedBy = CurrentUserId;

                    _dbContext.ContactImportTrackers.Update(contactImportTracker);
                    await _dbContext.SaveChangesAsync();

                }
                catch (Exception ex)
                {
                    contactImportTracker.status = UploadStatus.Failed;
                    contactImportTracker.Message = $"Upload failed: {ex.Message}";
                    contactImportTracker.UpdatedAt = DateTime.UtcNow;
                    _dbContext.ContactImportTrackers.Update(contactImportTracker);
                    await _dbContext.SaveChangesAsync();

                    _logger.LogError(ex, "Error occurred during customer bulk upload handler for trackerId: {TrackerId}, BusinessId: {BusinessId}, UserId: {UserId}, Type: {Type}", trackerId, BusinessId, CurrentUserId, Type);

                    throw;
                }
            }
            else
            {
                _logger.LogWarning("Contact import tracker not found for trackerId: {TrackerId}", trackerId);
            }
        }
        private async Task AddNewContactsAsync(List<Contacts> newContacts)
        {
            if (!newContacts.Any())
            {
                _logger.LogInformation("No new contacts to add.");
                return;
            }

            int batchSize = 1000;
            int totalRecords = newContacts.Count;
            int processedRecords = 0;

            while (processedRecords < totalRecords)
            {
                var batch = newContacts.Skip(processedRecords).Take(batchSize).ToList();

                try
                {
                    var inserted = await _genericRepository.InsertRecordsAsync("Contacts", StringHelper.GetPropertyNames<Contacts>(), batch);
                    if (!inserted)
                    {
                        throw new Exception("Batch insert failed.");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "⚠️ Failed to insert batch {BatchStart}-{BatchEnd}: {ErrorMessage}", processedRecords + 1, processedRecords + batchSize, ex.Message);
                }

                processedRecords += batchSize;
            }

            // Trigger workflows for new contacts after successful insertion
            await TriggerWorkflowsForNewContactsAsync(newContacts);
        }

        private async Task TriggerWorkflowsForNewContactsAsync(List<Contacts> newContacts)
        {
            try
            {
                if (!newContacts.Any())
                {
                    _logger.LogInformation("No new contacts to trigger workflows for.");
                    return;
                }

                var workflowNodes = await _dbContext.WorkflowNodes
                    .Include(n => n.Workflow)
                    .Where(n => n.Type == NodeType.FlowStart &&
                               n.Workflow.IsActive &&
                               !n.Workflow.IsDeleted &&
                               n.Workflow.CompanyId == newContacts.First().BusinessId.ToString())
                    .ToListAsync();

                var hasExcelWorkflow = workflowNodes.Any(n =>
                    n.PayloadModel?.FlowStartModel?.EntryNodeType == EntryNodeType.OnNewLead &&
                    n.PayloadModel?.FlowStartModel?.LeadSource != null &&
                    n.PayloadModel.FlowStartModel.LeadSource.Any(ls => ls.Source == SourceType.Excel.ToString()));

                if (!hasExcelWorkflow)
                {
                    _logger.LogInformation("No Excel-triggered workflows found for business {BusinessId}", newContacts.First().BusinessId);
                    return;
                }

                _logger.LogInformation("Found Excel-triggered workflows. Processing {ContactCount} contacts for workflow triggering", newContacts.Count);

                const int workflowBatchSize = 10;

                for (int i = 0; i < newContacts.Count; i += workflowBatchSize)
                {
                    var batch = newContacts.Skip(i).Take(workflowBatchSize).ToList();

                    foreach (var contact in batch)
                    {
                        try
                        {
                            await _nodeWorkflowEngineService.ProcessWorkflowAsync(contact, null, true);
                            _logger.LogDebug("Successfully triggered workflow for contact {ContactId}", contact.ContactId);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Failed to trigger workflow for contact {ContactId}: {ErrorMessage}", contact.ContactId, ex.Message);
                            await _logHistoryService.SaveErrorLogHistoryAsyn("TriggerWorkflowsForNewContactsAsync->Contact", contact.ContactId.ToString(), $"Processing contact: {contact.ContactId}", ex.Message, ex.StackTrace);
                        }
                    }
                    await Task.Delay(500); // Small delay between batches to avoid overwhelming the system
                }

                _logger.LogInformation("Completed workflow triggering for {ContactCount} contacts", newContacts.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to trigger workflows for new contacts: {ErrorMessage}", ex.Message);
                await _logHistoryService.SaveErrorLogHistoryAsyn("TriggerWorkflowsForNewContactsAsync->General", null, $"Processing {newContacts.Count} contacts", ex.Message, ex.StackTrace);
            }
        }

        public List<string> SplitTags(string tagsName)
        {
            List<string> tagList = new();
            if (!string.IsNullOrEmpty(tagsName))
            {
                foreach (string item in tagsName.Split(','))
                {
                    if (!string.IsNullOrEmpty(item?.Trim()))
                    {
                        tagList.Add(item.Trim());
                    }
                }
            }
            return tagList;
        }

        public List<Contacts> ConvertToCustomer(DataTable table, List<Tags> tags, string BusinessId, Guid CurrentUserId)
        {
            List<Contacts> customers = new List<Contacts>();
            foreach (DataRow row in table.Rows)
            {
                var tagNamesString = row.Table.Columns.Contains("Tags") ? row["Tags"]?.ToString() : null;

                // Get tag IDs instead of tag names
                List<string> tagIds = new List<string>();
                if (!string.IsNullOrEmpty(tagNamesString))
                {
                    var tagNames = SplitTags(tagNamesString);
                    foreach (var tagName in tagNames)
                    {
                        var normalizedTagName = tagName.Trim().ToLower();
                        var existingTag = tags.FirstOrDefault(t => t.Tag.Trim().ToLower().Equals(normalizedTagName, StringComparison.InvariantCultureIgnoreCase));
                        if (existingTag != null)
                        {
                            tagIds.Add(existingTag.Id.ToString());
                        }
                    }
                }

                var Contacts = new Contacts()
                {
                    ContactId = Guid.NewGuid(),
                    Name = row.Table.Columns.Contains("Name") ? row["Name"]?.ToString() : null,
                    Contact = row.Table.Columns.Contains("Contact") ? row["Contact"]?.ToString() : null,
                    Email = row.Table.Columns.Contains("Email") ? row["Email"]?.ToString() : null,
                    CountryCode = row.Table.Columns.Contains("CountryCode") ? row["CountryCode"]?.ToString() : null,
                    CountryName = row.Table.Columns.Contains("CountryName") ? row["CountryName"]?.ToString() : null,
                    Tags = tagIds.Any() ? string.Join(",", tagIds) : null, // Save tag IDs instead of tag names
                    ChatStatus = ChatStatus.New,
                    Source = SourceType.Excel,
                    BusinessId = Guid.Parse(BusinessId),
                    UserId = CurrentUserId,
                    CreatedDate = DateTime.UtcNow,
                    IsActive   = true,
                    IsSpam  =  false,
                    IsOptIn  =  Is_OptIn.optin,


                };
                customers.Add(Contacts);
            }
            return customers;
        }
        public List<Tags> GetTagFromName(string taging)
        {
            List<Tags> tagList = new();
            if (!string.IsNullOrEmpty(taging))
            {
                foreach (string item in taging.Split(','))
                {
                    if (!string.IsNullOrEmpty(item))
                    {
                        tagList.Add(new Tags { Tag = item });
                    }
                }
            }
            return tagList;
        }


    }
}
