using AutoMapper;
using DocumentFormat.OpenXml.Office2016.Drawing.ChartDrawing;
using Engageto.Attributes;
using Engageto.DependencyInjection;
using Engageto.Hubs;
using Engageto.Middleware;
using EngagetoBackGroundJobs;
using EngagetoContracts.AttributeName;
using EngagetoContracts.AutomationContracts;
using EngagetoContracts.CampaignContracts;
using EngagetoContracts.ContactContracts;
using EngagetoContracts.GeneralContracts;
using EngagetoContracts.IntegrationsContracts;
using EngagetoContracts.MetaContracts;
using EngagetoContracts.OptinContracts;
using EngagetoContracts.Services;
using EngagetoContracts.TemplateContracts;
using EngagetoContracts.UserContracts;
using EngagetoContracts.WebhookContracts.Client;
using EngagetoContracts.WebhookContracts.DownloadMedia;
using EngagetoContracts.WebhookContracts.ReceivedNotification;
using EngagetoContracts.WebhookContracts.SentMessage;
using EngagetoContracts.Workflow;
using EngagetoContracts.WorkflowRepository;
using EngagetoDapper;
using EngagetoDatabase.WebhookRepository.ReceiveTemplateRepo;
using EngagetoEntities.DdContext;
using EngagetoEntities.Mapping;
using EngagetoEntities.ServiceModels;
using EngagetoEntities.UserEntities.Models;
using EngagetoEntities.Validations;
using EngagetoEntities.Validations.TemplateValidation;
using EngagetoRepository.AutomationRepository;
using EngagetoRepository.CampaignRepository;
using EngagetoRepository.ContactsRepository;
using EngagetoRepository.GeneralServices;
using EngagetoRepository.IntegrationsRepository;
using EngagetoRepository.MetaServices;
using EngagetoRepository.OptInServices;
using EngagetoRepository.Repository;
using EngagetoRepository.Repostiories;
using EngagetoRepository.Services;
using EngagetoRepository.TemplateRepository;
using EngagetoRepository.UserRepository;
using EngagetoRepository.WebhookRepository.DownloadMediaRepo;
using EngagetoRepository.WebhookRepository.Hubs.Service;
using EngagetoRepository.WebhookRepository.ReceivedMessageRepo;
using EngagetoRepository.WebhookRepository.SentMessageRepo;
using EngagetoRepository.WebhookRepository.SentMessageService;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Razorpay.Api;
using System.Data;
using System.Reflection;
using System.Text;
using EngagetoBackGroundJobs.Interfaces;
using EngagetoBackGroundJobs.Implementation;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers().AddNewtonsoftJson(options =>
{
    options.SerializerSettings.Formatting = Newtonsoft.Json.Formatting.Indented;
    options.SerializerSettings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore;
});
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddHttpClient();
builder.Services.AddSignalR(options =>
{
    options.ClientTimeoutInterval = TimeSpan.FromSeconds(110); // Equivalent to ConnectionTimeout
    options.HandshakeTimeout = TimeSpan.FromSeconds(30); // You might want to use this for a disconnect timeout setting
    options.KeepAliveInterval = TimeSpan.FromSeconds(20); // Equivalent to KeepAlive
    options.EnableDetailedErrors = true;
    options.MaximumReceiveMessageSize = 10737418240; // 1 GB in bytes
    options.StreamBufferCapacity = 128;
});
builder.Services.AddSwaggerGen(c =>
{

    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "Authentication and Authorization ",
        Version = "v1"
    });
    c.OperationFilter<HeaderOperationFilter>();
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Name = "Authorization",
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer",
        BearerFormat = "JWT",
        In = ParameterLocation.Header,
        Description = "Here Enter JWT Token with Bearer Format line bearer[space] token"
    });
    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    { {
        new OpenApiSecurityScheme
        {
            Reference=new OpenApiReference
            {
                Type=ReferenceType.SecurityScheme,
                Id="Bearer"
            }
        },
        new string[]{}
        }
    });
});


//builder.Services.AddDbContext<OtpinDbContext>(options =>
//                options.UseSqlServer(builder.Configuration.GetConnectionString("ConnStr")));
builder.Services.AddDbContext<ApplicationDBContext>(options =>
                   options.UseSqlServer(builder.Configuration.GetConnectionString("ConnStr")));
//builder.Services.AddDbContextPool<CompaignDbContext>(options =>
//               options.UseSqlServer(builder.Configuration.GetConnectionString("ConnStr")));

//new context for all
builder.Services.AddDbContextFactory<ApplicationDbContext>(options =>
               options.UseSqlServer(builder.Configuration.GetConnectionString("ConnStr")));


builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
})
               .AddJwtBearer(options =>
               {
                   options.SaveToken = true;
                   options.RequireHttpsMetadata = false;
                   options.TokenValidationParameters = new TokenValidationParameters()
                   {
                       ValidateIssuer = true,
                       ValidateAudience = true,
                       ValidAudience = "https://loginAPI/api",
                       ValidIssuer = "https://dev-jvgzpy2caz4fvqso.us.auth0.com/",
                       IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes("Im0BochrmocQD992BKCFQvfcUsVdaJZBcTDb9TQ8wt-o9qIAzGH69rubwf5RAGGV"))
                   };
               });
//builder.Services.AddDbContext<automationDbContext>(options =>
//    options.UseSqlServer(builder.Configuration.GetConnectionString("ConnStr")));

// Register DapperConnectionFactory 
builder.Services.AddScoped<IDbConnection>(sp =>
{
    var connection = new SqlConnection(builder.Configuration.GetConnectionString("ConnStr"));
    return connection;
});

builder.Services.AddScoped<IUnitOfWork>(sp =>
{
    var dbConnection = sp.GetRequiredService<IDbConnection>();
    // 🔧 FIX: Don't open connection here, let UnitOfWork/operations handle it
    // Connection will be opened when first accessed through DapperConnectionFactory
    return new UnitOfWork(dbConnection);
});
builder.Services.Configure<DataProtectionTokenProviderOptions>(options => options.TokenLifespan = TimeSpan.FromHours(1));
builder.Services.Configure<SmtpSettings>(builder.Configuration.GetSection("SmtpSettings"));

builder.Services.AddBackgroundJobs(builder.Configuration);
builder.Services.AddScoped<IOptin, OptInRepository>();

builder.Services.AddScoped<IContactRepositoryBase, ContactRepositoryBase>();
builder.Services.AddScoped<IUserService, UserService>();
builder.Services.AddScoped<IAuthentication, Authentication>();
builder.Services.AddScoped<ITokenService, TokenService>();
builder.Services.AddScoped<ILogoutService, LogoutService>();
builder.Services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
builder.Services.AddScoped<INotificationPreferenceService, NotificationPreferenceService>();
builder.Services.AddScoped<IAccountDetailsService, AccountDetailsService>();
builder.Services.AddScoped<IHelpCenterService, HelpCenterService>();
builder.Services.AddScoped<ITeamDetailsService, TeamDetailsService>();
builder.Services.AddScoped<ICompanyDetailsService, CompanyDetailsService>();
builder.Services.AddScoped<IRazorpayPaymentService, PaymentsService>();
builder.Services.AddScoped<IRazorPayService, RazorPayService>();
builder.Services.AddScoped<IEmailService, EmailService>();
builder.Services.AddScoped<IForgotPasswordService, ForgotPasswordService>();
builder.Services.AddScoped<ILanguageService, LanguageService>();
builder.Services.AddScoped<IRoleService, RoleService>();
builder.Services.AddScoped<ICountryDetailsService, CountryDetailsService>();
builder.Services.AddScoped<IPermissionsService, PermissionsService>();
builder.Services.AddScoped<IClientDetailsService, ClientDetailsService>();
builder.Services.AddScoped<ICompanyDetailsService, CompanyDetailsService>();
builder.Services.AddScoped<IAhex_CRM_UserService, Ahex_CRM_UserService>();
builder.Services.AddScoped<IRazorPaymentGatewayService, RazorPaymentGatewayService>();
builder.Services.AddScoped<IWalletNotificationService, WalletNotificationService>();
builder.Services.AddScoped<IConversationAnalyticsService, ConversationAnalyticsCostService>();
builder.Services.AddScoped<ITransactionHistoryService, TransactionHistoryService>();
builder.Services.AddScoped<IDiscountService, DiscountService>();
builder.Services.AddScoped<IWalletService, WalletService>();
builder.Services.AddScoped<IPlanEntitiesService, PlanEntitiesService>();
builder.Services.AddScoped<IRepositoryBase, RepositoryBase>();
builder.Services.AddScoped<IAutoReplyCustomMessageRepository, AutoReplyCustomMessageRepository>();
builder.Services.AddScoped<IAutoReplyAutomationRepostry, AutoReplyAutomationRepostry>();
builder.Services.AddScoped<IUnitOfWork, UnitOfWork>();
builder.Services.AddScoped<IDownloadMedia, DownloadMeidaService>();
builder.Services.AddScoped<IMediaURL, MediaURL>();
builder.Services.AddScoped<IWhatsAppBusinessNotificarion, SentMessageStatusUpdate>();
builder.Services.AddScoped<IWhatsAppBusinessClient, SentMessageService>();
builder.Services.AddScoped<ISentMessage, SentMessage>();
builder.Services.AddScoped<TemplateStatus, ReceiveTemplateStatusUpdate>();
builder.Services.AddScoped<IReceivedContacts, ReceivedContacts>();
builder.Services.AddScoped<ICampaign, CampaignRepositories>();
builder.Services.AddScoped<IInboxService, InboxService>();
builder.Services.AddScoped<ITemplate, TemplatesService>();
builder.Services.AddScoped<IMetaPayloadService, MetaPayloadService>();
builder.Services.AddScoped<IMetaAuthenticationService, MetaAuthenticationService>();
builder.Services.AddScoped<IEnvironmentService, EnvironmentService>();
builder.Services.AddScoped<IMetaApiService, MetaApiService>();
builder.Services.AddScoped<IWidgetRepository, WidgetRepository>();
builder.Services.AddScoped<IConversationService, ConversationRepository>();
builder.Services.AddScoped<IWAWebhookMessageServiceAsync, WAWebhookMessageServiceAsync>();
builder.Services.AddScoped<TemplateValidation>();
builder.Services.AddScoped<IWorkflowService, WorkflowService>();
builder.Services.AddScoped<INodeWorkflowEngineService, NodeWorkflowEngineService>();
builder.Services.AddScoped<ICustomerWorkflowTrackerRepository, CustomerWorkflowTrackerRepository>();
builder.Services.AddScoped<IAttributeNameService, AttributeNameService>();
builder.Services.AddScoped<IWorkflowCustomResponseService, WorkflowCustomResponseService>();
builder.Services.AddScoped<IContactScheduler, ContactScheduler>();

// Firebase Services
builder.Services.AddScoped<EngagetoContracts.FirebaseContracts.IFirebaseConfigurationService, EngagetoRepository.FirebaseServices.FirebaseConfigurationService>();
builder.Services.AddScoped<EngagetoContracts.FirebaseContracts.IFirebasePushNotificationService, EngagetoRepository.FirebaseServices.FirebasePushNotificationService>();
builder.Services.AddScoped<EngagetoContracts.FirebaseContracts.IWhatsAppFirebaseNotificationService, EngagetoRepository.FirebaseServices.WhatsAppFirebaseNotificationService>();

// IGenericRepository is registered by AddInfraStructure() -> AddCommonStartup()
//builder.Services.AddDbContextPool<ContactsDbContext>(options =>
//         options.UseSqlServer(builder.Configuration.GetConnectionString("ConnStr"), b => b.MigrationsAssembly("ContactsContext")));
//builder.Services.AddDbContextPool<AppDbContext>(options =>
//         options.UseSqlServer(builder.Configuration.GetConnectionString("ConnStr"), b => b.MigrationsAssembly("WebhookContaxt")));
//builder.Services.AddDbContextPool<CompaignDbContext>(options =>
//         options.UseSqlServer(builder.Configuration.GetConnectionString("ConnStr"), b => b.MigrationsAssembly("CampaignContaxt")));


builder.Services.AddSingleton<FileValidator>();

builder.Services.AddInfraStructure(builder.Configuration);
builder.Services.AddSingleton<EngagetoDapper.Data.Connections.ISqlConnectionFactory, EngagetoDapper.Data.Connections.SqlConnectionFactory>();
builder.Services.AddTransientServices(typeof(ITransientService), ServiceLifetime.Transient);
builder.Services.AddTransientServices(typeof(ITransientServiceWithScoped), ServiceLifetime.Scoped);
builder.Services.AddConfigureService(builder.Configuration);
//Mapper register here
UserMapasterConfig.RegisterMappings();
TemplateMapping.RegisterMappings();
AutomationMapping.RegisterMappings();
//ConversationMapping.RegisterMappings();

builder.Services.AddSingleton<IMapper>(AutoMapperConfiguration.Configure());
var razorpayConfig = builder.Configuration.GetSection("Razorpay");
var razorpayClient = new RazorpayClient(razorpayConfig["ApiKey"], razorpayConfig["ApiSecret"]);
builder.Services.AddSingleton<RazorpayClient>(razorpayClient);
var origins = builder.Configuration.GetSection("Origins").GetChildren()
    .Select(x => x.Value)
    .ToArray();
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAllOrigins",
        builder =>
        {
            builder.WithOrigins(origins)
                   .AllowAnyMethod()
                   .AllowAnyHeader()
                   .AllowCredentials(); // Allow credentials if needed
        });
    options.AddPolicy("CustomCors", builder =>
    {
        builder.AllowAnyOrigin()
               .AllowAnyMethod()
               .AllowAnyHeader();
    });
});
builder.Services.AddScoped<ApplicationDBContext>();

var app = builder.Build();
app.UseHangfireDashboard(builder.Configuration);
// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment() || app.Environment.IsProduction())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}
app.UseHttpsRedirection();
app.UseCors("AllowAllOrigins");

app.UseAuthentication();
app.UseAuthorization();
//Middleware
app.UseMiddleware<EnvironmentDetectionMiddleware>();
app.UseMiddleware<ClaimsMiddleware>();

app.MapControllers();
app.MapHub<MessageHub>("/conversations");
app.Run();



