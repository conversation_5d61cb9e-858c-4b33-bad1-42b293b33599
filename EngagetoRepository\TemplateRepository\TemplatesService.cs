﻿using Amazon;
using Amazon.Runtime;
using Amazon.S3;
using Amazon.S3.Model;
using Amazon.S3.Transfer;
using EngagetoContracts.ContactContracts;
using EngagetoContracts.MetaContracts;
using EngagetoContracts.TemplateContracts;
using EngagetoContracts.UserContracts;
using EngagetoContracts.WebhookContracts.DownloadMedia;
using EngagetoContracts.WebhookContracts.SentMessage;
using EngagetoEntities;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.CommonDtos;
using EngagetoEntities.Dtos.ConversationDtos;
using EngagetoEntities.Dtos.MetaDto;
using EngagetoEntities.Dtos.TemplateDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.Utilities;
using EngagetoEntities.Validations.TemplateValidation;
using Humanizer;
using LibGit2Sharp;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Globalization;
using System.Net;
using System.Net.Http.Headers;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;


namespace EngagetoRepository.TemplateRepository
{
    public class TemplatesService : ITemplate

    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ApplicationDbContext _context;
        private readonly HttpClient _client = new HttpClient();
        private readonly IAccountDetailsService _accountDetailsService;
        private IConfiguration _configuration { get; set; }
        private readonly IMetaPayloadService _metaPayloadService;
        private readonly JsonSerializer _serializer = new JsonSerializer();
        private readonly IMetaApiService _metaApiService;
        private readonly EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IUserService _userService;
        private readonly EngagetoDapper.Data.Interfaces.CampaignInterfaces.ICampaignServices.IConversationsService _conversationsService;
        private readonly TemplateValidation _templateValidation;
        private IMediaURL _mediUrl;
        private readonly ICompanyDetailsService _companyDetailsService;
        private readonly IContactRepositoryBase _contactRepository;
        private readonly IWhatsAppBusinessNotificarion _whatsAppBusinessNotificarion;


        public TemplatesService(IHttpClientFactory _httpClient,
            IConfiguration configuration,
            ApplicationDbContext templateDbContext,
            IAccountDetailsService accountDetailsService,
            IMetaPayloadService metaPayloadService,
            IMetaApiService metaApiService,
            EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IUserService userService,
            EngagetoDapper.Data.Interfaces.CampaignInterfaces.ICampaignServices.IConversationsService conversationsService,
            TemplateValidation templateValidation,
            IMediaURL mediaURL,
            ICompanyDetailsService companyDetailsService,
            IContactRepositoryBase contactRepository,
            IWhatsAppBusinessNotificarion whatsAppBusinessNotificarion)
        {
            _context = templateDbContext;
            _configuration = configuration;
            _httpClientFactory = _httpClient;
            _accountDetailsService = accountDetailsService;
            _metaPayloadService = metaPayloadService;
            _metaApiService = metaApiService;
            _userService = userService;
            _templateValidation = templateValidation;
            _conversationsService = conversationsService;
            _mediUrl = mediaURL;
            _companyDetailsService = companyDetailsService;
            _contactRepository = contactRepository;
            _whatsAppBusinessNotificarion = whatsAppBusinessNotificarion;
        }
        CreateTemplateDto Templates = new CreateTemplateDto();
        SendHandlerDto Handler = new SendHandlerDto();

        public async Task<bool> IsTemplateExist(string businessId, string templateName)
        {
            if (string.IsNullOrEmpty(templateName))
                throw new Exception("TemplateName is required");
            bool result = StringHelper.IsValidTemplateName(templateName);
            if (!StringHelper.IsValidTemplateName(templateName))
                throw new Exception("Only lowercase letters, integers, and underscores are allowed in TemplateName.");
            if (templateName.Length > 512)
                throw new Exception("Maximum 512 characters allowed in TemplateName.");

            var existingTemplate = await _context.Templates
           .FirstOrDefaultAsync(t => t.BusinessId == businessId && t.TemplateName == templateName && t.Status != WATemplateStatus.DRAFT);

            if (existingTemplate != null)
                throw new Exception($"Template name '{templateName}' exists and it must be unique. ");
            return true;
        }


        public async Task<string> UploadMediaFile(UploadFileDto file)
        {
            var validationResult = _templateValidation.ValidateFile(file.File);
            if (validationResult != null)
                throw new Exception(validationResult.ToString());

            var accessKey = _configuration["Aws:AccessKey"];
            var secretKey = _configuration["Aws:SecretKey"];
            var bucketName = _configuration["Aws:BucketName"];
            var regionString = _configuration["Aws:Region"];
            var region = RegionEndpoint.GetBySystemName(regionString);
            var filepath = file.File.OpenReadStream();
            var credentials = new BasicAWSCredentials(accessKey, secretKey);
            var s3Client = new AmazonS3Client(credentials, region);
            var utility = new TransferUtility(s3Client);
            var fileExtension = Path.GetExtension(file.File.FileName);
            var fileName = $"{Guid.NewGuid()}_{DateTime.UtcNow}{fileExtension}";
            await utility.UploadAsync(filepath, bucketName, fileName);
            return "https://" + bucketName + ".s3." + regionString + ".amazonaws.com/" + fileName;
        }


        public async Task<Template> CreateTemplateAsync(CreateTemplateDto model, bool Draft)
        {
            var validationResult = await _templateValidation.ValidateCreateTemplate(model);
            if (validationResult != null)
            {
                var jsonString = JsonConvert.SerializeObject(validationResult);
                JObject jsonObject = JObject.Parse(jsonString);
                var innerMessage = jsonObject["Value"]?.ToString();
                if (!string.IsNullOrEmpty(innerMessage))
                {
                    JObject innerJson = JObject.Parse(innerMessage);
                    var actualMessage = innerJson["Message"]?.ToString();
                    throw new Exception(actualMessage);
                }
            }
            var metaResult = new Template();
            if (!string.IsNullOrEmpty(model.MediaFile))
            {
                var mediaValidationResult = await _templateValidation.ValidateCreateMediaFile(model);
                if (mediaValidationResult != null) throw new Exception($"{mediaValidationResult}");
            }

            var DraftTemplate = _context.Templates.Where(t => t.TemplateId == model.TemplateId && t.Status == WATemplateStatus.DRAFT).ToList();
            model.Body = model.Body.Replace("\"", "\\\"").Replace("\n", "\\n");
            var bodyMessage = model.Body;
            model.Body = StringHelper.ReplaceAndExtractVariables(bodyMessage).UpdatedMessage;
            model.Footer = !string.IsNullOrEmpty(model.Footer) ? model.Footer.Replace("\"", "\\\"").Replace("\n", "\\n") : null;
            model.Header = !string.IsNullOrEmpty(model.Header) ? model.Header.Replace("\"", "\\\"").Replace("\n", "\\n") : null;
            var headerMessage = model.Header;
            if (!string.IsNullOrEmpty(headerMessage))
            {
                model.Header = StringHelper.ReplaceAndExtractVariables(headerMessage).UpdatedMessage;
            }
            string HeaderHandle = null;
            HttpResponseMessage response = new HttpResponseMessage();
            var MediaTypes = Enum.GetName(typeof(MediaType), model.MediaType);
            var Categories = Enum.GetName(typeof(WATemplateCategory), model.Category);
            var Language = Enum.GetName(typeof(EngagetoEntities.Enums.Language), model.Language);

            var Business = await _context.BusinessDetailsMetas.FirstOrDefaultAsync(t => t.BusinessId == model.BusinessId);

            var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == model.UserId);

            _client.DefaultRequestHeaders.Add("Authorization", "Bearer " + Business.Token);

            if (MediaTypes != "TEXT" && MediaTypes != "NONE")
            {
                Handler = await GetHeaderHandle(model);
                HeaderHandle = Handler.Headerhandler;
                if (HeaderHandle == null)
                {
                    // Handle null result from GetHeaderHandle
                    return null; // or throw an exception
                }
            }

            if (!Draft)
            {
                //model.MediaType == NONE,TEXT,IMAGE,VIDEO,DOCUMENT.
                string jsonData = $@"
                                 {{
                                 ""name"": ""{model.TemplateName.ToLower()}"",
                                 ""language"": ""{model.Language}"",
                                ""category"": ""{Categories}"",
                                 ""components"": [
                                   ";

                if (MediaTypes.ToLower() != "none")
                {
                    if (HeaderHandle == null || HeaderHandle == "")
                    {
                        // Check if exactly one placeholder is present in the header
                        Regex regex1 = new Regex(@"\{\{\d+\}\}");
                        bool containsPlaceholders1 = regex1.IsMatch(model.Header);
                        int placeholderCount1 = regex1.Matches(model.Header).Count;


                        // Text Template With  variable {{1}}.
                        //if exactly one placeholder (with variable {{1}}) is present in the header then add this json
                        if (containsPlaceholders1 && placeholderCount1 == 1)
                        {
                            // Placeholder for body_text
                            StringBuilder headerTextPlaceholders = new StringBuilder();
                            for (int i = 1; i <= placeholderCount1; i++)
                            {
                                headerTextPlaceholders.Append($@"""{i}""");
                                if (i < placeholderCount1)
                                    headerTextPlaceholders.Append(",");
                            }
                            jsonData += $@"
                                    {{
                                        ""type"": ""HEADER"",
                                         {(MediaTypes.ToLower() == "text" ? "\"format\": \"TEXT\"" : $"\"format\": \"{MediaTypes}\"")},
                                                                                                        ""text"": ""{model.Header}"",
                                                                                                        ""example"": {{
                                                                                                        ""header_text"": [{headerTextPlaceholders.ToString()}]
                                                                                                       }}
                                      }},";

                        }
                        else
                        {
                            // Text Template Without  variable {{1}}.
                            //if no placeholder (without variable {{1}}) is present in the header then add this json
                            jsonData += $@"
                                          {{
                                            ""type"": ""HEADER"",
                                            {(MediaTypes.ToLower() == "text" ? "\"format\": \"TEXT\"" : $"\"format\": \"{MediaTypes}\"")},
                                                                                                           ""text"": ""{model.Header}""

                                           }},";

                        }
                    }

                    else
                    {

                        // Media (image video document) Template.
                        jsonData += $@"
                            {{
                              ""type"": ""HEADER"",
                               {(MediaTypes.ToLower() == "text" ? "\"format\": \"TEXT\"" : $"\"format\": \"{MediaTypes}\"")},
                              ""example"": {{
                              ""header_handle"": [
                                                  ""{HeaderHandle}""
                                                 ]
                            }}
                      }},";
                    }
                }


                //if placeholder for body with variable {\{\d\}\}
                Regex regex2 = new Regex(@"\{\{\d+\}\}");
                Regex regex3 = new Regex(@"\s*\{\{\d+\}\}\s+.*?(?=\b\w+\b).*?\s\{\{\d+\}\}\s*", RegexOptions.Singleline);
                bool containsPlaceholders2 = regex2.IsMatch(model.Body);
                bool containsPlaceholders3 = regex3.IsMatch(model.Body);
                int placeholderCount = regex2.Matches(model.Body).Count;
                if (containsPlaceholders2)
                {

                    // Extract text segments between placeholders
                    var segments = regex2.Split(model.Body);
                    int wordsBetweenVariablesCount = 0;
                    foreach (var segment in segments)
                    {
                        string trimmedSegment = segment.Trim();
                        if (!string.IsNullOrWhiteSpace(trimmedSegment))
                        {
                            // Count words in the segment
                            string[] words = trimmedSegment.Split(new char[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
                            wordsBetweenVariablesCount += words.Length;
                        }
                    }
                    int variableLength = placeholderCount;
                    if (wordsBetweenVariablesCount <= 2 * variableLength)
                    {
                        throw new Exception("This template contains too many variable parameters relative to the message length. You need to decrease the number of variable parameters or increase the message length.");
                    }

                    StringBuilder bodyTextPlaceholders = new StringBuilder();
                    for (int i = 1; i <= placeholderCount; i++)
                    {
                        bodyTextPlaceholders.Append($@"""{i}""");
                        if (i < placeholderCount)
                            bodyTextPlaceholders.Append(",");
                    }
                    jsonData += $@"
                                    {{
                                    ""type"": ""BODY"",
                                    ""text"": ""{model.Body}"",
                                    ""example"": {{
                                    ""body_text"": [[{bodyTextPlaceholders.ToString()}]]
                                    }}
                                }},
                           {{
                                      ""type"": ""FOOTER"",
                                      ""text"": ""{model.Footer}""
                                }}";
                }
                else
                {
                    //if placeholder for body without variable {\{\d\}\}
                    jsonData += $@"
                                  {{
                                      ""type"": ""BODY"",
                                      ""text"": ""{model.Body}""
                                  }},
                                 {{
                                     ""type"": ""FOOTER"",
                                      ""text"": ""{model.Footer}""
                                   }}";

                }

                JArray buttonsArray = new JArray();

                bool buttonsAvailable = (!string.IsNullOrEmpty(model.PhoneNumber)
                   || (model.UrlButtonName != null && model.UrlButtonName.Count > 0 && model.RedirectUrl != null && model.RedirectUrl.Count == model.UrlButtonName.Count)
                   || (model.QuickReply != null && model.QuickReply.Count > 0));
                if (buttonsAvailable)
                {

                    string PhoneNumbers = null;

                    if (!string.IsNullOrEmpty(model.CountryCode) && !string.IsNullOrEmpty(model.PhoneNumber) && !string.IsNullOrEmpty(model.CallButtonName))
                    {
                        PhoneNumbers = model.CountryCode.TrimStart('+') + model.PhoneNumber;
                    }

                    if (!string.IsNullOrEmpty(model.CountryCode))
                    {
                        if (!_context.CountryDetails.Select(m => m.CountryCode).Contains(model.CountryCode))
                        {
                            throw new Exception($"Country code is not valid or not found.");
                        }
                    }
                    if (!string.IsNullOrEmpty(model.PhoneNumber))
                    {
                        // Adding only one phone button
                        JObject phoneButton = new JObject
                        {
                            { "type", "PHONE_NUMBER" },
                            { "text", model.CallButtonName }, // Assuming you want the first CallButtonName
                            { "phone_number", PhoneNumbers } // Assuming you want the phone number corresponding to the first CallButtonName
                        };

                        buttonsArray.Add(phoneButton);
                    }


                    Regex regex = new Regex(@"\{\{\d+\}\}$"); // Match a variable placeholder at the end of the URL

                    if (model.UrlButtonName != null && model.UrlButtonName.Count > 0 && model.RedirectUrl != null && model.RedirectUrl.Count == model.UrlButtonName.Count)
                    {
                        int maxButtons = 2;

                        for (int i = 0; i < model.UrlButtonName.Count && i < maxButtons; i++)
                        {
                            string redirectUrl = model.RedirectUrl[i];

                            if (string.IsNullOrEmpty(model.UrlButtonName[i]) || string.IsNullOrEmpty(redirectUrl))
                            {
                                throw new ArgumentException("UrlButtonName or redirectUrl cannot be empty or null.");
                            }

                            JObject UrlButton = new JObject
                               {
                                { "type", "URL" },
                                { "text", model.UrlButtonName[i] },
                                { "url", redirectUrl }
                             };

                            // Check if the URL contains the variable placeholder at the end
                            if (regex.IsMatch(redirectUrl))
                            {
                                // Extract the dynamic example from the URL
                                string dynamicExample = redirectUrl.Substring(redirectUrl.LastIndexOf("{{") + 2, redirectUrl.LastIndexOf("}}") - redirectUrl.LastIndexOf("{{") - 2);

                                // Add the dynamic example to the JSON
                                UrlButton.Add("example", new JArray { dynamicExample });
                            }

                            buttonsArray.Add(UrlButton);
                        }
                    }

                    if (model.QuickReply != null && model.QuickReply.Count > 0)
                    {
                        int maxButtons = 10;

                        foreach (string quickReplyText in model.QuickReply)
                        {
                            if (string.IsNullOrEmpty(quickReplyText))
                            {
                                throw new ArgumentException("quickReplyText cannot be empty or null.");
                            }
                            if (buttonsArray.Count >= maxButtons)
                                break;

                            JObject quickReplyButton = new JObject
                            {
                               { "type", "QUICK_REPLY" },
                               { "text", quickReplyText }
                            };
                            buttonsArray.Add(quickReplyButton);
                        }
                    }
                    JObject buttonsComponent = new JObject
                    {
                       { "type", "BUTTONS" },
                       { "buttons", buttonsArray }
                    };
                    jsonData += $@",{buttonsComponent}";
                }

                // Close the JSON data string
                jsonData += @"

               ]}";

                JObject json = JObject.Parse(jsonData);

                if (string.IsNullOrEmpty(model.Footer))
                {
                    json["components"].FirstOrDefault(c => c["type"].ToString() == "FOOTER")?.Remove();
                }

                //Act for the None Template
                if (MediaTypes.ToLower() == "none")
                {
                    json["components"].FirstOrDefault(c => c["type"].ToString() == "HEADER")?.Remove();
                }


                var request = new HttpRequestMessage(HttpMethod.Post, $"https://graph.facebook.com/v23.0/{Business.WhatsAppBusinessAccountID}/message_templates");
                request.Content = new StringContent(json.ToString(), Encoding.UTF8, "application/json");
                response = await _client.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    await using var stream = await response.Content.ReadAsStreamAsync();
                    using var reader = new StreamReader(stream);
                    using var jsonReader = new JsonTextReader(reader);
                    metaResult = _serializer.Deserialize<EngagetoEntities.Entities.Template>(jsonReader);

                    // Assuming _context is your Entity Framework DbContext
                    WATemplateStatus status;
                    switch (metaResult.Status)
                    {
                        case (WATemplateStatus)1:
                            status = WATemplateStatus.PENDING;
                            break;
                        case (WATemplateStatus)2:
                            status = WATemplateStatus.APPROVED;
                            break;
                        case (WATemplateStatus)3:
                            status = WATemplateStatus.REJECTED;
                            break;
                        default:
                            // Log or debug the unexpected status value
                            Console.WriteLine($"Unexpected status value: {metaResult.Status}");
                            status = WATemplateStatus.PENDING; // Assign a default status
                            break;
                    }

                    if (DraftTemplate == null || !DraftTemplate.Any())
                    {
                        var templateEntity1 = new EngagetoEntities.Entities.Template
                        {
                            MetaId = metaResult.MetaId,
                            Category = metaResult.Category,
                            SubCategory = model.SubCategory,
                            TemplateName = model.TemplateName,
                            LanguageCode = model.Language.ToString(),
                            MediaType = model.MediaType,
                            MediaAwsUrl = Handler.S3Url,
                            Header = headerMessage,
                            Body = bodyMessage,
                            Footer = model.Footer,
                            Status = status,
                            BusinessId = Business.BusinessId,
                            UserId = model.UserId,
                            Createdby = user.Name,
                            CreatedDate = DateTime.UtcNow,
                            UpdatedDate = DateTime.UtcNow,
                            Updatedby = user.Name,
                        };
                        var newTemplate = _context.Templates.Add(templateEntity1);
                        // Save data to ButtonDetails table
                        await _context.SaveChangesAsync();

                        Guid TemplateId = newTemplate.Entity.TemplateId;

                        if (!string.IsNullOrEmpty(model.CallButtonName) && !string.IsNullOrEmpty(model.PhoneNumber) || !string.IsNullOrEmpty(model.CountryCode))
                        {
                            string PhoneNumbers = model.CountryCode.TrimStart('+') + model.PhoneNumber;

                            EngagetoEntities.Entities.Button ba = new();
                            ba.TemplateId = TemplateId;
                            ba.ButtonType = "PHONE_NUMBER";
                            ba.ButtonName = model.CallButtonName;
                            ba.ButtonValue = PhoneNumbers;
                            _context.ButtonDetails.Add(ba);
                        }
                        if (model.UrlButtonName != null && model.UrlButtonName.Count != 0 && model.RedirectUrl != null && model.RedirectUrl.Count != 0)
                        {
                            for (int i = 0; i < model.UrlButtonName.Count; i++)
                            {
                                EngagetoEntities.Entities.Button b = new();
                                b.TemplateId = TemplateId;
                                b.ButtonType = "URL";
                                b.ButtonName = model.UrlButtonName[i];
                                b.ButtonValue = model.RedirectUrl[i];
                                _context.ButtonDetails.Add(b);
                            }
                        }
                        if (model.QuickReply != null)
                        {
                            if (model.QuickReply.Count != 0)
                            {
                                for (int i = 0; i < model.QuickReply.Count; i++)
                                {
                                    EngagetoEntities.Entities.Button b = new();
                                    b.TemplateId = TemplateId;
                                    b.ButtonType = "QUICK_REPLY";
                                    b.ButtonValue = model.QuickReply[i];
                                    _context.ButtonDetails.Add(b);
                                }
                            }
                        }
                        await _context.SaveChangesAsync();
                    }
                    else
                    {
                        // Update existing draft templates
                        foreach (var draft in DraftTemplate)
                        {
                            draft.TemplateId = (Guid)model.TemplateId;
                            draft.MetaId = metaResult.MetaId;
                            draft.Category = metaResult.Category;
                            draft.SubCategory = model.SubCategory;
                            draft.TemplateName = model.TemplateName;
                            draft.LanguageCode = model.Language.ToString();
                            draft.MediaType = model.MediaType;
                            draft.MediaAwsUrl = Handler.S3Url;
                            draft.Header = model.Header;
                            draft.Body = model.Body;
                            draft.Footer = model.Footer;
                            draft.Status = status;
                            draft.BusinessId = Business.BusinessId;
                            draft.UserId = model.UserId;
                            draft.Createdby = user.Name;
                            draft.CreatedDate = DateTime.UtcNow;
                            draft.UpdatedDate = DateTime.UtcNow;
                            draft.Updatedby = user.Name;
                        }
                        _context.Templates.UpdateRange(DraftTemplate);
                        await _context.SaveChangesAsync();

                        Guid TemplateId = (Guid)model.TemplateId;

                        var existingButtons = _context.ButtonDetails.Where(b => b.TemplateId == TemplateId).ToList();
                        _context.ButtonDetails.RemoveRange(existingButtons);
                        await _context.SaveChangesAsync();

                        if (!string.IsNullOrEmpty(model.CallButtonName) && !string.IsNullOrEmpty(model.PhoneNumber) || !string.IsNullOrEmpty(model.CountryCode))
                        {
                            string PhoneNumbers = model.CountryCode.TrimStart('+') + model.PhoneNumber;
                            EngagetoEntities.Entities.Button ba = new();
                            ba.TemplateId = TemplateId;
                            ba.ButtonType = "PHONE_NUMBER";
                            ba.ButtonName = model.CallButtonName;
                            ba.ButtonValue = PhoneNumbers;
                            _context.ButtonDetails.Add(ba);
                        }
                        if (model.UrlButtonName != null && model.UrlButtonName.Count != 0 && model.RedirectUrl != null && model.RedirectUrl.Count != 0)
                        {
                            for (int i = 0; i < model.UrlButtonName.Count; i++)
                            {
                                EngagetoEntities.Entities.Button b = new();
                                b.TemplateId = TemplateId;
                                b.ButtonType = "URL";
                                b.ButtonName = model.UrlButtonName[i];
                                b.ButtonValue = model.RedirectUrl[i];
                                _context.ButtonDetails.Add(b);
                            }
                        }
                        if (model.QuickReply != null)
                        {
                            if (model.QuickReply.Count != 0)
                            {
                                for (int i = 0; i < model.QuickReply.Count; i++)
                                {
                                    EngagetoEntities.Entities.Button b = new();
                                    b.TemplateId = TemplateId;
                                    b.ButtonType = "QUICK_REPLY";
                                    b.ButtonValue = model.QuickReply[i];
                                    _context.ButtonDetails.Add(b);
                                }
                            }
                        }
                        await _context.SaveChangesAsync();
                    }
                }
            }
            else
            {
                // Save the entity as a draft
                string language = model.Language.ToString();
                if (DraftTemplate == null || !DraftTemplate.Any())
                {

                    var templateEntity = new EngagetoEntities.Entities.Template
                    {
                        Category = model.Category,
                        SubCategory = model.SubCategory,
                        TemplateName = model.TemplateName,
                        MediaType = model.MediaType,
                        LanguageCode = model.Language.ToString(),
                        MediaAwsUrl = Handler.S3Url,
                        Header = headerMessage,
                        Body = bodyMessage,
                        Footer = model.Footer,
                        Status = WATemplateStatus.DRAFT,
                        BusinessId = Business.BusinessId,
                        UserId = model.UserId,
                        Createdby = user.Name,
                        CreatedDate = DateTime.UtcNow,
                        UpdatedDate = DateTime.UtcNow,
                        Updatedby = user.Name,
                    };
                    var newTemplate = _context.Templates.Add(templateEntity);
                    await _context.SaveChangesAsync();

                    Guid TemplateId1 = newTemplate.Entity.TemplateId;

                    if (!string.IsNullOrEmpty(model.CallButtonName) && !string.IsNullOrEmpty(model.PhoneNumber) || !string.IsNullOrEmpty(model.CountryCode))
                    {
                        string PhoneNumbers = model.CountryCode.TrimStart('+') + model.PhoneNumber;
                        EngagetoEntities.Entities.Button ba = new();
                        ba.TemplateId = TemplateId1;
                        ba.ButtonType = "PHONE_NUMBER";
                        ba.ButtonName = model.CallButtonName;
                        ba.ButtonValue = PhoneNumbers;
                        _context.ButtonDetails.Add(ba);
                    }
                    if (model.UrlButtonName != null && model.UrlButtonName.Count != 0 && model.RedirectUrl != null && model.RedirectUrl.Count != 0)
                    {
                        for (int i = 0; i < model.UrlButtonName.Count; i++)
                        {
                            EngagetoEntities.Entities.Button b = new();
                            b.TemplateId = TemplateId1;
                            b.ButtonType = "URL";
                            b.ButtonName = model.UrlButtonName[i];
                            b.ButtonValue = model.RedirectUrl[i];
                            _context.ButtonDetails.Add(b);
                        }
                    }
                    if (model.QuickReply != null && model.QuickReply.Count != 0)
                    {
                        for (int i = 0; i < model.QuickReply.Count; i++)
                        {
                            EngagetoEntities.Entities.Button b = new();
                            b.TemplateId = TemplateId1;
                            b.ButtonType = "QUICK_REPLY";
                            b.ButtonValue = model.QuickReply[i];
                            _context.ButtonDetails.Add(b);
                        }
                    }
                    await _context.SaveChangesAsync();
                }
                else
                // Update existing draft templates
                if (DraftTemplate != null || DraftTemplate.Any())
                {
                    foreach (var draft in DraftTemplate)
                    {
                        draft.TemplateId = (Guid)model.TemplateId;
                        draft.Category = model.Category;
                        draft.SubCategory = model.SubCategory;
                        draft.TemplateName = model.TemplateName;
                        draft.LanguageCode = model.Language.ToString();
                        draft.MediaType = model.MediaType;
                        draft.MediaAwsUrl = Handler.S3Url;
                        draft.Header = headerMessage;
                        draft.Body = bodyMessage;
                        draft.Footer = model.Footer;
                        draft.Status = WATemplateStatus.DRAFT;
                        draft.BusinessId = Business.BusinessId;
                        draft.UserId = model.UserId;
                        draft.Createdby = user.Name;
                        draft.CreatedDate = DateTime.UtcNow;
                        draft.UpdatedDate = DateTime.UtcNow;
                        draft.Updatedby = user.Name;
                    }
                    _context.Templates.UpdateRange(DraftTemplate);
                    await _context.SaveChangesAsync();

                    Guid TemplateId = (Guid)model.TemplateId;

                    var existingButtons = _context.ButtonDetails.Where(b => b.TemplateId == TemplateId).ToList();
                    _context.ButtonDetails.RemoveRange(existingButtons);
                    await _context.SaveChangesAsync();


                    if (!string.IsNullOrEmpty(model.CallButtonName) && !string.IsNullOrEmpty(model.PhoneNumber) || !string.IsNullOrEmpty(model.CountryCode))
                    {
                        string PhoneNumbers = model.CountryCode.TrimStart('+') + model.PhoneNumber;
                        EngagetoEntities.Entities.Button ba = new();
                        ba.TemplateId = TemplateId;
                        ba.ButtonType = "PHONE_NUMBER";
                        ba.ButtonName = model.CallButtonName;
                        ba.ButtonValue = PhoneNumbers;
                        _context.ButtonDetails.Add(ba);
                    }
                    if (model.UrlButtonName != null && model.UrlButtonName.Count != 0 && model.RedirectUrl != null && model.RedirectUrl.Count != 0)
                    {
                        for (int i = 0; i < model.UrlButtonName.Count; i++)
                        {
                            EngagetoEntities.Entities.Button b = new();
                            b.TemplateId = TemplateId;
                            b.ButtonType = "URL";
                            b.ButtonName = model.UrlButtonName[i];
                            b.ButtonValue = model.RedirectUrl[i];
                            _context.ButtonDetails.Add(b);
                        }
                    }
                    if (model.QuickReply != null)
                    {
                        if (model.QuickReply.Count != 0)
                        {
                            for (int i = 0; i < model.QuickReply.Count; i++)
                            {
                                EngagetoEntities.Entities.Button b = new();
                                b.TemplateId = TemplateId;
                                b.ButtonType = "QUICK_REPLY";
                                b.ButtonValue = model.QuickReply[i];
                                _context.ButtonDetails.Add(b);
                            }
                        }
                    }
                    await _context.SaveChangesAsync();
                }
            }
            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                var errorResponse = JsonConvert.DeserializeObject<CreateTempateErrorResponseDto>(errorContent);
                throw new Exception(errorResponse?.ErrrorMessage.ErrorUserMsg);
            }
            return metaResult;
        }


        public async Task<Template> AddTemplateAsync(CreateTemplateDto model, bool Draft, Guid currentUserId)
        {

            var existingTemplates = await _context.Templates
                .Where(t => (t.TemplateName == model.TemplateName
                    || (t.Footer != null && t.Footer == model.Footer)
                    || t.Body == model.Body)
                    ).ToListAsync();

            if (existingTemplates.Any(t => t.TemplateName == model.TemplateName && t.Status != WATemplateStatus.DRAFT))
                throw new Exception("TemplateName must be unique. A template with this name already exists.");

            if (existingTemplates.Any(t => t.Body == model.Body && t.Status != WATemplateStatus.DRAFT))
                throw new Exception("Body must be unique. A template with this body already exist.");

            if (!string.IsNullOrEmpty(model.Footer) && existingTemplates.Any(t => t.Footer == model.Footer && t.Status != WATemplateStatus.DRAFT))
                throw new Exception("Footer must be unique. A template with this footer already exist.");

            var mediaErrors = await model.ValidateMedia();
            if (mediaErrors.Any()) throw new Exception("Invalid media file");

            var basePayload = CreateMetaPayloadJsonDto.CreateBasePayload(model.TemplateName, model.Language.ToString(), model.Category.ToString());
            var jsonPayload = JObject.Parse(basePayload);

            var components = jsonPayload["components"] as JArray ?? new JArray();

            if (MediaType.NONE != model.MediaType)
            {
                SendHandlerDto sendHandler = new SendHandlerDto();
                if (!string.IsNullOrEmpty(model.MediaFile))
                    sendHandler = await GetHeaderHandle(model);
                components.Add(await _metaPayloadService.AddHeaderForCreateComponentRequest(model, sendHandler?.Headerhandler ?? string.Empty));
            }

            components.Add(_metaPayloadService.AddBodyComponentRequest(model.Body));
            _metaPayloadService.AddFooterForCreateComponentRequest(model, ref components);
            var buttons = _metaPayloadService.AddButtonForCreateComponetRequest(model, ref components);
            var template = model.Adapt<Template>();
            var bodyMessage = StringHelper.FormateString(template.Body);
            template.Body = StringHelper.ReplaceAndExtractVariables(bodyMessage).UpdatedMessage;
            var headerMessage = StringHelper.FormateString(template.Header ?? string.Empty);
            if (!string.IsNullOrEmpty(template.Header))
            {
                model.Header = StringHelper.ReplaceAndExtractVariables(headerMessage).UpdatedMessage;
            }
            //preparing the template send body
            var sendTemplateDto = PrepareWhatsappTemplate(model);

            template.SendTemplateJsonDto = JsonConvert.SerializeObject(sendTemplateDto);

            if (!Draft)
            {
                var draftTemplates = existingTemplates.Where(x => x.Status == WATemplateStatus.DRAFT && x.TemplateName == model.TemplateName);
                foreach (var draftTemplate in draftTemplates)
                    await DeleteTemplate(draftTemplate.TemplateId);
                var metaAcount = await _userService.IsValidateMetaAccount(model.BusinessId, model.UserId);
                var url = MetaApi.GetCreateTemplateUrl(metaAcount?.WhatsAppBusinessAccountID ?? "");
                var response = await TemplateMetaApiAsync<CreateTemplateResponse>(url, jsonPayload, metaAcount.Token);
                template.Body = bodyMessage;
                template.Header = headerMessage;
                template.Status = Enum.Parse<WATemplateStatus>(response.Status, true);
                template.MetaId = response?.Id;
            }
            else
            {
                template.Status = WATemplateStatus.DRAFT;
            }

            var resultTemplate = await SaveTemplateAsync(template, currentUserId);
            var resultButtons = await SaveButtonsAsync(buttons, resultTemplate.TemplateId);
            resultTemplate.Buttons = resultButtons;

            return resultTemplate;
        }


        public async Task<Template> UpdateTemplateAsync(EditTemplateDto editModel, Guid currentUserId)
        {
            var existingTemplate = await _context.Templates
                .FirstOrDefaultAsync(t => t.TemplateId == editModel.TemplateId)
                ?? throw new NotFoundException("Not Found Template.");

            var errors = new List<string>();
            var metaAcount = await _userService.IsValidateMetaAccount(editModel.BusinessId, editModel.UserId);
            var model = editModel.Adapt<CreateTemplateDto>();
            model.TemplateName = existingTemplate?.TemplateName ?? string.Empty;
            model.Category = existingTemplate?.Category ?? WATemplateCategory.MARKETING;
            model.Language = (EngagetoEntities.Enums.Language)Enum.Parse(typeof(EngagetoEntities.Enums.Language), existingTemplate?.LanguageCode ?? "en", true);

            model.ValidateTemplate(ref errors);

            if (existingTemplate == null)
                throw new Exception("Not Found Template.");

            if (_context.Templates.Any(t => t.TemplateId != editModel.TemplateId && (t.Body == model.Body && t.Status != WATemplateStatus.DRAFT)))
                errors.Add("Body must be unique. A template with this body already exist.");

            var mediaErrors = await model.ValidateMedia();

            errors.AddRange(mediaErrors ?? new List<string>());

            if (errors.Count > 0)
            {
                throw new Exception("Template is invalid!");
            }

            var basePayload = CreateMetaPayloadJsonDto.CreateBasePayload(model.TemplateName, model.Language.ToString(), model.Category.ToString());

            var jsonPayload = JObject.Parse(basePayload);

            var components = jsonPayload["components"] as JArray ?? new JArray();

            if (MediaType.NONE != model.MediaType)
            {
                SendHandlerDto sendHandler = new SendHandlerDto();
                if (!string.IsNullOrEmpty(model.MediaFile))
                    sendHandler = await GetHeaderHandle(model);
                components.Add(await _metaPayloadService.AddHeaderForCreateComponentRequest(model, sendHandler?.Headerhandler ?? string.Empty));
            }

            components.Add(_metaPayloadService.AddBodyComponentRequest(model.Body));
            _metaPayloadService.AddFooterForCreateComponentRequest(model, ref components);
            var buttons = _metaPayloadService.AddButtonForCreateComponetRequest(model, ref components);

            var template = editModel.Adapt(existingTemplate);
            //preparing the template send body
            var sendTemplateDto = PrepareWhatsappTemplate(model);

            template.SendTemplateJsonDto = JsonConvert.SerializeObject(sendTemplateDto);

            if (existingTemplate.Status != WATemplateStatus.DRAFT)
            {
                var url = MetaApi.GetUpdateTemplateUrl(existingTemplate.MetaId ?? string.Empty);
                var response = await TemplateMetaApiAsync<UpdateTemplateResponse>(url, jsonPayload, metaAcount?.Token ?? "");
                if (response?.Success ?? false)
                    template.Status = WATemplateStatus.PENDING;
            }
            else
            {
                template.Status = WATemplateStatus.DRAFT;
            }

            var resultTemplate = await SaveTemplateAsync(template, currentUserId);
            var resultButtons = await SaveButtonsAsync(buttons, resultTemplate.TemplateId);
            resultTemplate.Buttons = resultButtons;

            return resultTemplate;

        }

        public async Task<object> GetAllTemplateFilterAsync(FilterOperationsDto Operations, string BusinessId, Guid UserId, int page, int per_page)
        {
            var Business = await _context.BusinessDetailsMetas.FirstOrDefaultAsync(t => t.BusinessId == BusinessId);
            var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == UserId);
            var list = await _context.Templates
               .Include(i => i.Buttons).Where(i => i.Feature == null).ToListAsync();

            if (Business == null || user == null)
            {
                throw new Exception("Business or User with the specified ID does not exist.");
            }

            if (string.IsNullOrEmpty(Business?.BusinessId) || user?.Id == Guid.Empty)
            {
                throw new Exception("BusinessId or UserId is invalid.");
            }

            if (Operations.Filtering.Conditions.Any(c => c.Value == ((int)WATemplateStatus.DELETED).ToString()))
            {
                list = list.Where(m => m.BusinessId.ToLower() == BusinessId.ToLower() && m.IsDeleted == !Operations.IsDeleted).ToList();
            }
            else
            {
                list = list.Where(m => m.BusinessId.ToLower() == BusinessId.ToLower() && m.IsDeleted == Operations.IsDeleted).ToList();
            }

            if (!string.Equals(user?.CompanyId, BusinessId, StringComparison.OrdinalIgnoreCase))
            {
                throw new Exception("User does not have access to the specified business.");
            }

            if (Operations != null)
            {
                if (Operations.Searching != null)
                {
                    if (!string.IsNullOrEmpty(Operations.Searching.Value))
                    {
                        var searchingValue = Operations.Searching.Value.ToLower();
                        list = list.Where(m => (m.TemplateName != null && m.TemplateName.ToLower().Contains(searchingValue)) ||
                                 (m.Status != null && Enum.TryParse<WATemplateStatus>(searchingValue, true, out var status) && m.Status == status) ||
                                 (m.SubCategory != null && m.SubCategory.ToLower().Contains(searchingValue)))
                         .ToList();

                    }
                }
                // Filter
                if (Operations?.Filtering != null)
                {
                    list = ApplyFilter(list, Operations.Filtering);
                }

                //DateRangeFilter
                if (Operations.DateRangeFilters != null && Operations.DateRangeFilters.Any())
                {
                    foreach (var dateFilter in Operations.DateRangeFilters)
                    {
                        if (string.IsNullOrEmpty(dateFilter.Column) ||
                            string.IsNullOrEmpty(dateFilter.FromDate) ||
                            string.IsNullOrEmpty(dateFilter.ToDate))
                            continue;

                        if (DateTime.TryParse(dateFilter.FromDate, out var fromDate) &&
                            DateTime.TryParse(dateFilter.ToDate, out var toDate))
                        {
                            toDate = toDate.Date.AddDays(1);

                            list = list.Where(item =>
                            {
                                var property = typeof(Template).GetProperty(dateFilter.Column, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                                if (property == null) return false;

                                var value = property.GetValue(item);
                                if (value is DateTime dateValue)
                                {
                                    return dateValue >= fromDate && dateValue < toDate;
                                }

                                return false;
                            }).ToList();
                        }
                    }
                }

                // Sorting
                if (Operations?.Sorting != null)
                {
                    if (Operations.Sorting.Column != "" && Operations.Sorting.Column != null)
                    {
                        Operations.Sorting.Column = Operations.Sorting.Column.ToLower();
                        Operations.Sorting.Order = Operations.Sorting.Order?.ToLower();

                        list = Operations.Sorting.Order == "desc" ? list.OrderByDescending(m => GetValue(m, Operations.Sorting.Column)).ToList() :
                                                                    list.OrderBy(m => GetValue(m, Operations.Sorting.Column)).ToList();

                        object GetValue(object item, string propertyName)
                        {
                            var property = item.GetType().GetProperty(propertyName, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                            if (property != null)
                            {
                                var value = property.GetValue(item, null);
                                // Handle if property is DateTime
                                if (value is DateTime dateTimeValue)
                                {
                                    return dateTimeValue;
                                }
                                return value; // Return as is for non-DateTime properties
                            }
                            throw new ArgumentException($"Property '{propertyName}' not found.");
                        }
                    }
                }
            }

            // Pagination
            int Total = list.Count;
            int no_pages = list.Count() / per_page;
            list = list.OrderByDescending(m => m.UpdatedDate).ToList();
            if ((list.Count - (no_pages * per_page)) > 0)
            {
                no_pages++;
            }

            if (list != null)
            {
                var statusResponse = await _metaApiService.GetAllTemplatesAsync(Business);
                foreach (var template in list)
                {
                    if (template.Status == WATemplateStatus.PENDING)
                    {
                        var matchingStatus = statusResponse.FirstOrDefault(status => status.Name == template.TemplateName);
                        if (matchingStatus?.Status != null)
                        {
                            var updatedStatus = Enum.Parse<WATemplateStatus>(matchingStatus.Status, true);
                            if (template.Status != updatedStatus)
                            {
                                template.Status = updatedStatus;
                                _context.Templates.Update(template);
                                await _context.SaveChangesAsync();
                            }
                        }
                    }
                }
            }

            // Validating the page is there are not and enter page number is there or not
            // Pagination based on page and per_page
            List<EngagetoEntities.Entities.Template> data = list.Skip((page - 1) * per_page).Take(per_page).ToList();
            list = data;
            var result = new
            {
                Total = Total,
                page = page,
                per_page = per_page,
                data = list.OrderByDescending(m => m.UpdatedDate)
            };

            return result;
        }

        private JObject PrepareWhatsappTemplate(CreateTemplateDto template)
        {
            JObject basePayload = _metaPayloadService.InitializeBasePayload("#Contact#", template.TemplateName, template.Language.ToString());
            var components = _metaPayloadService.GetComponents();
            string? headerValue = template.MediaType == MediaType.NONE ? null : template.MediaType == MediaType.TEXT ? "#HeaderValue#" : template.MediaFile;
            _metaPayloadService.AddHeaderComponent(ref components, template.Header, template.MediaType, headerValue);

            var bodyVeriableCount = StringHelper.GetVariableCount(template.Body);

            var sampleBodyPayloads = Enumerable.Range(0, bodyVeriableCount).Select(x => $"#Sample{x + 1}#").ToList();
            _metaPayloadService.AddBodyComponent(ref components, template.Body, sampleBodyPayloads);

            if (basePayload["template"] is JObject templateObject)
            {
                templateObject["components"] = components;
            }
            return basePayload;
        }

        private async Task<T> TemplateMetaApiAsync<T>(string url, JObject jsonPayload, string token)
        {
            using (var httpClient = new HttpClient())
            {
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                var content = new StringContent(jsonPayload.ToString(), Encoding.UTF8, "application/json");
                var response = await httpClient.PostAsync(url, content);
                if (!response.IsSuccessStatusCode) throw new Exception("Failed to call TemplateMetaApi");

                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<T>(responseContent) ?? Activator.CreateInstance<T>();
            }
        }

        // code Code for creating HeaderHandle then call into Create Image Templates method.
        public async Task<SendHandlerDto> GetHeaderHandle(CreateTemplateDto model)
        {

            var Business = await _context.BusinessDetailsMetas.Where(t => t.BusinessId == model.BusinessId).FirstOrDefaultAsync();
            var user = await _context.Users.Where(u => u.Id == model.UserId).FirstOrDefaultAsync();

            if (Business == null || user == null)
            {
                throw new Exception("Business or User with the specified ID does not exist.");
            }
            if (string.IsNullOrEmpty(Business.BusinessId) || user.Id == Guid.Empty)
            {
                throw new Exception("BusinessId or UserId is invalid.");
            }
            if (!string.Equals(user.CompanyId, model.BusinessId, StringComparison.OrdinalIgnoreCase))
            {
                throw new Exception("User does not have access to the specified business.");
            }
            SendHandlerDto HeaderUrl = new SendHandlerDto();
            if (model.MediaFile == null)
            {
                // Handle empty model.File upload
                return HeaderUrl;
            }

            var client = _httpClientFactory.CreateClient();
            var res = await client.GetAsync(model.MediaFile);
            var contentType = res.Content.Headers.ContentType?.MediaType;
            var fileSize = res.Content.Headers.ContentLength ?? 0;
            var fileName = model.MediaFile.Split('/').Last();
            var fileType = contentType;
            byte[] fileBytes = await res.Content.ReadAsByteArrayAsync();
            HeaderUrl.S3Url = model.MediaFile;
            var sessionId = "";
            using (var httpClient = new HttpClient())
            {
                var uploadUrl = $"https://graph.facebook.com/v23.0/{Business.AppId}/uploads";
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", Business.Token);
                var metadata = new { file_length = $"{fileSize}", file_type = $"{fileType}", file_name = $"{fileName}" };
                var metadataJson = JsonConvert.SerializeObject(metadata);
                var metadataContent = new StringContent(metadataJson, Encoding.UTF8, "application/json");
                var uploadResponse = await httpClient.PostAsync(uploadUrl, metadataContent);
                if (!uploadResponse.IsSuccessStatusCode)
                {
                    // Handle upload failure
                    return null;
                }
                var uploadResponseContent = await uploadResponse.Content.ReadAsStringAsync();
                var uploadResult = JsonConvert.DeserializeObject<SendHandlerDto>(uploadResponseContent);
                sessionId = uploadResult.Id;
                string whatsAppBusinessEndpoint = $"https://graph.facebook.com/v23.0/{sessionId}";
                HttpClient _httpClient = new HttpClient();
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("OAuth", Business.Token);
                _httpClient.DefaultRequestHeaders.Add("file_offset", "0");
                string boundary = $"----------{Guid.NewGuid():N}";
                var content = new MultipartFormDataContent(boundary);
                HttpResponseMessage? response;
                ByteArrayContent mediaFileContent = new ByteArrayContent(fileBytes);
                HttpRequestMessage requestMessage = new HttpRequestMessage();
                requestMessage.Method = HttpMethod.Post;
                requestMessage.Content = mediaFileContent;
                requestMessage.Content.Headers.ContentType = MediaTypeHeaderValue.Parse("application/x-www-form-urlencoded");
                requestMessage.RequestUri = new Uri($"{_httpClient.BaseAddress}{whatsAppBusinessEndpoint}");
                response = await _httpClient.SendAsync(requestMessage).ConfigureAwait(false);
                var responseContent = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<SendHandlerDto>(responseContent);
                var handle = result.h;
                HeaderUrl.Headerhandler = handle;
                return HeaderUrl;
            }
        }

        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        private bool CanEditTemplate(EngagetoEntities.Entities.Template template)
        {
            // Get the current datetime
            var now = DateTime.UtcNow;

            // Filter edits for the last 30 days
            var last30Days = now.AddDays(-30);
            var recentEdits30Days = _context.Templates
                .Where(e => e.TemplateId == template.TemplateId && e.UpdatedDate >= last30Days).Count();

            // Filter edits for the last 24 hours
            var last24Hours = now.AddHours(-24);
            var recentEdits24Hours = _context.Templates
                .Where(e => e.TemplateId == template.TemplateId && e.UpdatedDate >= last24Hours).Count();

            // Check if the template has reached the edit limits
            return recentEdits30Days < 10 && recentEdits24Hours < 1;
        }

        public async Task<HttpResponseMessage> EditTemplateAsync(EditTemplateDto model)
        {
            var validationResult = await _templateValidation.ValidateEditTemplate(model);
            if (validationResult != null)
                throw new Exception($"{validationResult}");

            if (!string.IsNullOrEmpty(model.MediaFile))
            {
                var mediaValidationResult = await _templateValidation.ValidateEditMediaTemplate(model);
                if (mediaValidationResult != null) throw new Exception($"{mediaValidationResult}");
            }

            string HeaderHandle = null;
            HttpResponseMessage response = new HttpResponseMessage();

            model.Body = model.Body.Replace("\"", "\\\"").Replace("\n", "\\n");
            var bodyMessage = model.Body;
            model.Body = StringHelper.ReplaceAndExtractVariables(bodyMessage).UpdatedMessage;
            model.Footer = !string.IsNullOrEmpty(model.Footer) ? model.Footer.Replace("\"", "\\\"").Replace("\n", "\\n") : null;
            model.Header = !string.IsNullOrEmpty(model.Header) ? model.Header.Replace("\"", "\\\"").Replace("\n", "\\n") : null;
            var headerMessage = model.Header;
            if (!string.IsNullOrEmpty(headerMessage))
            {
                model.Header = StringHelper.ReplaceAndExtractVariables(headerMessage).UpdatedMessage;
            }
            var template = await _context.Templates.Where(t => t.TemplateId == model.TemplateId).FirstOrDefaultAsync();

            var Business = await _context.BusinessDetailsMetas.FirstOrDefaultAsync(t => t.BusinessId == model.BusinessId);
            var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == model.UserId);

            if (Business == null || user == null)
            {
                throw new Exception("Business or User with the specified ID does not exist.");
            }
            if (string.IsNullOrEmpty(Business.BusinessId) || user.Id == Guid.Empty)
            {
                throw new Exception("BusinessId or UserId is invalid.");
            }
            if (!string.Equals(user.CompanyId, model.BusinessId, StringComparison.OrdinalIgnoreCase))
            {
                throw new Exception("User does not have access to the specified business.");
            }
            _client.DefaultRequestHeaders.Add("Authorization", "Bearer " + Business.Token);

            if (template == null)
            {
                return new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("Template not found")
                };
            }

            var MediaTypes = Enum.GetName(typeof(MediaType), model.MediaType);
            Templates.MediaFile = model.MediaFile;
            Templates.MediaType = model.MediaType;
            Templates.BusinessId = model.BusinessId;
            Templates.UserId = model.UserId;

            if (MediaTypes != "TEXT" && MediaTypes != "NONE")
            {
                Handler = await GetHeaderHandle(Templates);
                HeaderHandle = Handler.Headerhandler;

                if (HeaderHandle == null)
                {
                    // Handle null result from GetHeaderHandle
                    return null; // or throw an exception
                }
            }


            //model.MediaType == NONE,TEXT,IMAGE,VIDEO,DOCUMENT.
            string jsonData = $@"
                                  {{
                                  ""name"": ""{template.TemplateName}"",
                                  ""language"": ""{template.LanguageCode}"",
                                 ""category"": ""{template.Category}"",
                                  ""components"": [
                                    ";



            if (MediaTypes.ToLower() != "none")
            {
                if (HeaderHandle == null || HeaderHandle == "")
                {
                    // Check if exactly one placeholder is present in the header
                    Regex regex1 = new Regex(@"\{\{\d+\}\}"); // For checking variable parameter formatting
                    bool containsPlaceholders1 = regex1.IsMatch(model.Header);
                    int placeholderCount1 = regex1.Matches(model.Header).Count;

                    if (containsPlaceholders1 && placeholderCount1 == 1)
                    {
                        // Placeholder for body_text
                        StringBuilder headerTextPlaceholders = new StringBuilder();
                        for (int i = 1; i <= placeholderCount1; i++)
                        {
                            headerTextPlaceholders.Append($@"""{i}""");
                            if (i < placeholderCount1)
                                headerTextPlaceholders.Append(",");
                        }
                        jsonData += $@"
                                     {{
                                         ""type"": ""HEADER"",
                                          {(MediaTypes.ToLower() == "text" ? "\"format\": \"TEXT\"" : $"\"format\": \"{MediaTypes}\"")},
                                          ""text"": ""{model.Header}"",
                                           ""example"": {{
                                         ""header_text"": [{headerTextPlaceholders.ToString()}]
                                         }}
                                        }},";
                    }
                    else
                    {
                        // Text Template Without  variable {{1}}.
                        //if no placeholder (without variable {{1}}) is present in the header then add this json
                        jsonData += $@"
                                           {{
                                             ""type"": ""HEADER"",
                                             {(MediaTypes.ToLower() == "text" ? "\"format\": \"TEXT\"" : $"\"format\": \"{MediaTypes}\"")},
                                                                                                            ""text"": ""{model.Header}""

                                            }},";

                    }
                }

                else
                {
                    jsonData += $@"
                             {{
                               ""type"": ""HEADER"",
                                {(MediaTypes.ToLower() == "text" ? "\"format\": \"TEXT\"" : $"\"format\": \"{MediaTypes}\"")},
                               ""example"": {{
                               ""header_handle"": [
                                                   ""{HeaderHandle}""
                                                  ]
                             }}
                       }},";
                }
            }



            // Close the JSON data string
            Regex regex2 = new Regex(@"\{\{\d+\}\}");
            Regex regex3 = new Regex(@"\s*\{\{\d+\}\}\s+.*?(?=\b\w+\b).*?\s\{\{\d+\}\}\s*", RegexOptions.Singleline);
            bool containsPlaceholders2 = regex2.IsMatch(model.Body);
            bool containsPlaceholders3 = regex3.IsMatch(model.Body);
            int placeholderCount = regex2.Matches(model.Body).Count;

            if (containsPlaceholders2)
            {

                // Extract text segments between placeholders
                var segments = regex2.Split(model.Body);
                int wordsBetweenVariablesCount = 0;

                foreach (var segment in segments)
                {
                    string trimmedSegment = segment.Trim();
                    if (!string.IsNullOrWhiteSpace(trimmedSegment))
                    {
                        // Count words in the segment
                        string[] words = trimmedSegment.Split(new char[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
                        wordsBetweenVariablesCount += words.Length;
                    }
                }
                int variableLength = placeholderCount;
                if (wordsBetweenVariablesCount <= 2 * variableLength)
                {
                    throw new Exception("This template contains too many variable parameters relative to the message length. You need to decrease the number of variable parameters or increase the message length.");
                }


                StringBuilder bodyTextPlaceholders = new StringBuilder();
                for (int i = 1; i <= placeholderCount; i++)
                {
                    bodyTextPlaceholders.Append($@"""{i}""");
                    if (i < placeholderCount)
                        bodyTextPlaceholders.Append(",");
                }
                jsonData += $@"
                                   {{
                                     ""type"": ""BODY"",
                                     ""text"": ""{model.Body}"",
                                     ""example"": {{
                                     ""body_text"": [[{bodyTextPlaceholders.ToString()}]]
                                     }}
                                 }},
                             {{
                                       ""type"": ""FOOTER"",
                                       ""text"": ""{model.Footer}""
                                 }}";
            }
            else
            {
                jsonData += $@"
                                   {{
                                       ""type"": ""BODY"",
                                       ""text"": ""{model.Body}""
                                   }},
                                  {{
                                      ""type"": ""FOOTER"",
                                       ""text"": ""{model.Footer}""
                                    }}";

            }

            JArray buttonsArray = new JArray();
            bool buttonsAvailable = (!string.IsNullOrEmpty(model.PhoneNumber)
               || (model.UrlButtonName != null && model.UrlButtonName.Count > 0 && model.RedirectUrl != null && model.RedirectUrl.Count == model.UrlButtonName.Count)
               || (model.QuickReply != null && model.QuickReply.Count > 0));

            if (buttonsAvailable)
            {
                if (!string.IsNullOrEmpty(model.CountryCode))
                {
                    if (!_context.CountryDetails.Select(m => m.CountryCode).Contains(model.CountryCode))
                    {
                        throw new Exception($"Country code is not valid or not found.");
                    }

                    string PhoneNumbers = model.CountryCode.TrimStart('+') + model.PhoneNumber;

                    if (!string.IsNullOrEmpty(model.PhoneNumber))
                    {
                        if (string.IsNullOrEmpty(model.CallButtonName) || string.IsNullOrEmpty(PhoneNumbers))
                        {
                            throw new ArgumentException("PhoneNumbers or CallButtonName cannot be empty or null.");
                        }

                        JObject phoneButton = new JObject
                         {
                            { "type", "PHONE_NUMBER" },
                            { "text", model.CallButtonName }, // Assuming you want the first CallButtonName
                            { "phone_number", PhoneNumbers } // Assuming you want the phone number corresponding to the first CallButtonName
                         };
                        buttonsArray.Add(phoneButton);
                    }
                }

                Regex regex = new Regex(@"\{\{\d+\}\}$"); // Match a variable placeholder at the end of the URL

                if (model.UrlButtonName != null && model.UrlButtonName.Count > 0 && model.RedirectUrl != null && model.RedirectUrl.Count == model.UrlButtonName.Count)
                {
                    int maxButtons = 2;

                    for (int i = 0; i < model.UrlButtonName.Count && i < maxButtons; i++)
                    {
                        string redirectUrl = model.RedirectUrl[i];
                        if (string.IsNullOrEmpty(model.UrlButtonName[i]) || string.IsNullOrEmpty(redirectUrl))
                        {
                            throw new ArgumentException("UrlButtonName or redirectUrl cannot be empty or null.");
                        }
                        JObject UrlButton = new JObject
                              {
                                 { "type", "URL" },
                                 { "text", model.UrlButtonName[i] },
                                 { "url", redirectUrl }
                              };

                        // Check if the URL contains the variable placeholder at the end
                        if (regex.IsMatch(redirectUrl))
                        {
                            // Extract the dynamic example from the URL
                            string dynamicExample = redirectUrl.Substring(redirectUrl.LastIndexOf("{{") + 2, redirectUrl.LastIndexOf("}}") - redirectUrl.LastIndexOf("{{") - 2);

                            // Add the dynamic example to the JSON
                            UrlButton.Add("example", new JArray { dynamicExample });
                        }

                        buttonsArray.Add(UrlButton);
                    }
                }

                if (model.QuickReply != null && model.QuickReply.Count > 0)
                {
                    int maxButtons = 10;

                    foreach (string quickReplyText in model.QuickReply)
                    {
                        if (buttonsArray.Count >= maxButtons)
                            break; // Exit the loop if the maximum number of buttons is reached
                        if (string.IsNullOrEmpty(quickReplyText))
                        {
                            throw new ArgumentException("quickReplyText cannot be empty or null.");
                        }
                        JObject quickReplyButton = new JObject
                             {
                                { "type", "QUICK_REPLY" },
                                { "text", quickReplyText }
                             };
                        buttonsArray.Add(quickReplyButton);
                    }
                }

                JObject buttonsComponent = new JObject
                     {
                        { "type", "BUTTONS" },
                        { "buttons", buttonsArray }
                     };
                jsonData += $@",{buttonsComponent}";
            }

            // Close the JSON data string
            jsonData += @"
              ]}";

            JObject json = JObject.Parse(jsonData);

            if (string.IsNullOrEmpty(model.Footer))
            {
                json["components"].FirstOrDefault(c => c["type"].ToString() == "FOOTER")?.Remove();
            }

            if (MediaTypes.ToLower() == "none")
            {
                json["components"].FirstOrDefault(c => c["type"].ToString() == "HEADER")?.Remove();
            }

            var request = new HttpRequestMessage(HttpMethod.Post, $"https://graph.facebook.com/v23.0/{template.MetaId}");
            request.Content = new StringContent(json.ToString(), Encoding.UTF8, "application/json");
            response = await _client.SendAsync(request);
            if (response.IsSuccessStatusCode)
            {
                await using var stream = await response.Content.ReadAsStreamAsync();
                using var reader = new StreamReader(stream);
                using var jsonReader = new JsonTextReader(reader);
                var result = _serializer.Deserialize<EngagetoEntities.Entities.Template>(jsonReader);
                WATemplateStatus status;
                switch (result.Status)
                {
                    case (WATemplateStatus)1:
                        status = WATemplateStatus.PENDING;
                        break;
                    case (WATemplateStatus)2:
                        status = WATemplateStatus.APPROVED;
                        break;
                    case (WATemplateStatus)3:
                        status = WATemplateStatus.REJECTED;
                        break;
                    default:

                        // Log or debug the unexpected status value
                        Console.WriteLine($"Unexpected status value: {result.Status}");
                        status = WATemplateStatus.PENDING; // Assign a default status
                        break;
                }
                var existingTemplate = _context.Templates.FirstOrDefault(t => t.TemplateId == model.TemplateId);

                if (existingTemplate != null)
                {
                    // Update the properties with the new values
                    existingTemplate.MetaId = template.MetaId;
                    existingTemplate.Category = template.Category;
                    existingTemplate.SubCategory = model.SubCategory;
                    existingTemplate.TemplateName = template.TemplateName;
                    existingTemplate.LanguageCode = template.LanguageCode;
                    existingTemplate.MediaType = model.MediaType;
                    existingTemplate.MediaAwsUrl = Handler.S3Url;
                    existingTemplate.Header = headerMessage;
                    existingTemplate.Body = bodyMessage;
                    existingTemplate.Footer = model.Footer;
                    existingTemplate.Status = status;
                    existingTemplate.BusinessId = Business.BusinessId;
                    existingTemplate.UserId = model.UserId;
                    existingTemplate.Updatedby = user.Name;
                    existingTemplate.UpdatedDate = DateTime.UtcNow;
                }
                _context.Templates.Update(existingTemplate);

                await _context.SaveChangesAsync();

                _context.ButtonDetails.Where(m => m.TemplateId == model.TemplateId);


                //Button DB = new Button { TemplateId = model.TemplateId };
                _context.ButtonDetails.RemoveRange(_context.ButtonDetails.Where(m => m.TemplateId == model.TemplateId));

                Guid TemplateId = model.TemplateId;
                if (!string.IsNullOrEmpty(model.CallButtonName))
                {
                    string PhoneNumber = model.CountryCode.TrimStart('+') + model.PhoneNumber;
                    EngagetoEntities.Entities.Button ba = new();
                    ba.TemplateId = TemplateId;
                    ba.ButtonType = "PHONE_NUMBER";
                    ba.ButtonName = model.CallButtonName;
                    ba.ButtonValue = PhoneNumber;
                    _context.ButtonDetails.Add(ba);
                }
                if (model.UrlButtonName != null)
                {
                    for (int i = 0; i < model.UrlButtonName.Count; i++)
                    {
                        EngagetoEntities.Entities.Button b = new();
                        b.TemplateId = TemplateId;
                        b.ButtonType = "URL";
                        b.ButtonName = model.UrlButtonName[i];
                        b.ButtonValue = model.RedirectUrl[i];
                        _context.ButtonDetails.Add(b);
                    }
                }
                if (model.QuickReply != null)
                {
                    for (int i = 0; i < model.QuickReply.Count; i++)
                    {
                        EngagetoEntities.Entities.Button b = new();
                        b.TemplateId = TemplateId;
                        b.ButtonType = "QUICK_REPLY";
                        b.ButtonValue = model.QuickReply[i];
                        _context.ButtonDetails.Add(b);
                    }
                }
                await _context.SaveChangesAsync();
            }

            return response;
        }

        public async Task<EngagetoEntities.Entities.Template> SaveTemplateAsync(EngagetoEntities.Entities.Template templateDto, Guid userId)
        {
            var user = await _accountDetailsService.GetAccountDetails(userId);

            if (templateDto.TemplateId == Guid.Empty || templateDto?.TemplateId == null)
            {
                templateDto.TemplateId = Guid.NewGuid();
                templateDto.Createdby = user.Name;
                templateDto.CreatedDate = DateTime.UtcNow;
                templateDto.Updatedby = user.Name;
                templateDto.UpdatedDate = DateTime.UtcNow;
                await _context.Templates.AddAsync(templateDto);
            }
            else
            {
                var template = await _context.Templates.FirstOrDefaultAsync(t => t.TemplateId == templateDto.TemplateId);
                if (template is not null)
                {
                    template = templateDto.Adapt(template);
                    template.Updatedby = user.Name;
                    template.UpdatedDate = DateTime.UtcNow;
                    _context.Templates.Update(template);
                }
            }

            var result = await _context.SaveChangesAsync();
            return templateDto;
        }

        public async Task<List<EngagetoEntities.Entities.Button>> SaveButtonsAsync(List<EngagetoEntities.Entities.Button> buttons, Guid templateId)
        {
            var strategy = _context.Database.CreateExecutionStrategy();

            return await strategy.ExecuteAsync(async () =>
            {
                await using var transaction = await _context.Database.BeginTransactionAsync();
                try
                {
                    await DeleteButtonAsync(templateId);
                    buttons.ForEach(x => x.TemplateId = templateId);
                    await _context.ButtonDetails.AddRangeAsync(buttons);
                    await _context.SaveChangesAsync();

                    await transaction.CommitAsync();
                    return buttons;
                }
                catch (Exception)
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            });
        }

        public async Task DeleteButtonAsync(Guid templateId)
        {
            var templatesToRemove = _context.ButtonDetails.Where(x => x.TemplateId == templateId);
            if (await templatesToRemove.AnyAsync())
            {
                _context.ButtonDetails.RemoveRange(templatesToRemove);
            }
        }
        public async Task<bool> DeleteTemplate(Guid templateId)
        {
            var template = await _context.Templates.FirstOrDefaultAsync(x => x.TemplateId == templateId);
            if (template != null)
            {
                _context.Templates.RemoveRange(template);
                await _context.SaveChangesAsync();
                return true;
            }
            return false;
        }
        /// <summary>
        /// This SendTemplateModel Api use for send the template with (variable or without variable).
        /// </summary>
        /// <param name="SendTemplateModel"></param>
        /// <returns></returns>
        public async Task<SendHandlerDto> SendTemplateS3(SendTemplateDto model)
        {
            SendHandlerDto HeaderUrl = new SendHandlerDto();
            if (model.MediaFile == null)
            {
                // Handle empty model.MediaFile upload
                return HeaderUrl;
            }
            HeaderUrl.S3Url = model.MediaFile;
            return HeaderUrl;
        }

        public bool HasVariables(string text)
        {
            return Regex.IsMatch(text, @"\{\{\d+\}\}");
        }

        public string ReplaceVariable(string text, string[]? values)
        {
            for (int i = 0; i < values.Length; i++)
            {
                text = text.Replace($"{{{{{i + 1}}}}}", values[i]); // Assuming each example value is an array of one element
            }
            return text;
        }

        public async Task<HttpResponseMessage> SendTemplateAsync(SendTemplateDto model)
        {
            await _conversationsService.VerifySubscriptionAndExpectedWalletBalanceAysc(model.BusinessId, 1);

            var Business = await _context.BusinessDetailsMetas.FirstOrDefaultAsync(t => t.BusinessId == model.BusinessId);
            var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == model.UserId);
            if (Business == null || user == null)
                throw new Exception("Business or User with the specified ID does not exist.");

            if (!string.Equals(user.CompanyId, model.BusinessId, StringComparison.OrdinalIgnoreCase))
            {
                throw new Exception("User does not have access to the specified business.");
            }
            _client.DefaultRequestHeaders.Add("Authorization", "Bearer " + Business.Token);

            var template = _context.Templates.FirstOrDefault(t => t.TemplateId == model.TemplateId);
            if (template == null)
                throw new NotFoundException("Template not found.");


            if (model.Contact == null || !model.Contact.Any())
                throw new Exception("At least one contact is required.");
            HttpResponseMessage response = null;

            template.Buttons = _context.ButtonDetails.Where(m => m.TemplateId == template.TemplateId).ToList();
            var urls = template.Buttons.Where(b => b.ButtonType == "URL").ToList();
            string TemplateHeader = template.Header ?? string.Empty;
            string TemplateBody = template.Body;
            string modifiedUrls = string.Join(",", urls.Select(b => b.ButtonValue));

            var variableRegex = StringHelper.GetVariableRegexs();
            if (template != null && TemplateBody != null && variableRegex.IsMatch(TemplateBody))
            {
                var replacedBody = template.Body;
                var matches = Regex.Matches(replacedBody, @"\{\{(\d+)\}\}");
                int index = matches.Count();

                if (model.BodyVariableValues == null || index != model.BodyVariableValues.Count())
                {
                    throw new Exception("BodyVariableValues is required and should contain all acurate Variable Values (not more ot less Values) .");
                }
                int a = 0;
                foreach (Match match in matches)
                {
                    if (index > 0 && index <= model.BodyVariableValues.Length)
                    {
                        var bodyValueParts = model.BodyVariableValues[a].Split(',');
                        if (index > 0)
                        {
                            replacedBody = replacedBody.Replace(match.Value, bodyValueParts[0].Trim());
                        }
                        a++;
                    }
                    index--;
                }
                TemplateBody = replacedBody;

                if (TemplateBody.Length < 1 || TemplateBody.Length > 1024)
                {
                    throw new Exception("Body Length must be between 1 and 1024 characters.");
                }
            }

            if (template != null && TemplateHeader != null)
            {
                if (variableRegex.IsMatch(template.Header ?? string.Empty))
                {
                    if (model.HeaderVariableValue == null)
                    {
                        throw new Exception("HeaderVariableValue is required");
                    }
                    if (model.HeaderVariableValue != null)
                    {
                        List<string> headerList = new List<string>();
                        headerList.Add(model.HeaderVariableValue);
                        TemplateHeader = StringHelper.ReplacePlaceholders(template.Header ?? string.Empty, headerList);

                        if (TemplateHeader.Length > 60)
                        {
                            throw new Exception("Header length must be between 0 and 60 characters and " +
                                                           " match the required pattern: only one variable {{1}} allowed with exactly one space before and after the variable.");
                        }
                    }
                }
            }

            if (model.RedirectUrlVariableValues != null && model.RedirectUrlVariableValues.Length > 2)
            {
                throw new Exception("Only up to 2 RedirectUrlVariableValues are allowed.");
            }
            if (model.MediaFile != null || model.MediaFile?.Length > 0)
            {
                var result = await _mediUrl.ValidateMediaFile(model.MediaFile);
                if (!result.IsValid) throw new Exception($@"{result.ErrorMessage}");
            }
            foreach (var phoneNumberId in model.Contact)
            {
                if (phoneNumberId != null)
                {

                    string To = phoneNumberId;

                    if (!string.IsNullOrEmpty(phoneNumberId))
                    {


                        template.Buttons = _context.ButtonDetails.Where(m => m.TemplateId == template.TemplateId).ToList();

                        // Separate buttons by type
                        var urlButtons = template.Buttons.Where(b => b.ButtonType == "URL").ToList();
                        var phoneNumberButtons = template.Buttons.FirstOrDefault(b => b.ButtonType == "PHONE_NUMBER");
                        var quickReplyButtons = template.Buttons.Where(b => b.ButtonType == "QUICK_REPLY").ToList();

                        if (template == null)
                        {
                            return new HttpResponseMessage(HttpStatusCode.NotFound);
                        }
                        var MadiaUrls = template.MediaAwsUrl;

                        if (model.MadiaUrl != null || model.MediaFile != null)
                        {
                            MadiaUrls = model.MadiaUrl != null ? model.MadiaUrl : Handler.S3Url;
                            if (model.MediaFile != null)
                            {
                                var Handler = await SendTemplateS3(model);
                                MadiaUrls = Handler.S3Url;
                            }
                        }

                        if (model.BodyVariableValues != null && model.BodyVariableValues.Length > 0)
                        {
                            for (int a = 0; a < model.BodyVariableValues.Length; a++)
                            {
                                if (!string.IsNullOrEmpty(model.BodyVariableValues[a]))
                                {
                                    model.BodyVariableValues[a] = model.BodyVariableValues[a]
                                        .Replace("\"", "\\\"")
                                        .Replace("\n", "\\n");
                                }
                            }
                        }
                        else
                        {
                            model.BodyVariableValues = model.BodyVariableValues;
                        }
                        model.HeaderVariableValue = !string.IsNullOrEmpty(model.HeaderVariableValue) ? model.HeaderVariableValue.Replace("\"", "\\\"").Replace("\n", "\\n") : null;

                        //for save into database
                        string TempHeader = template.Header;
                        string TemptBody = template.Body;
                        string TemptButtonQuickReply = String.Join(",", urlButtons.Select(m => m.ButtonName));
                        string TemptButtonRedirectUrl = string.Join(",", urlButtons.Select(b => b.ButtonValue));
                        string TemptButtonRedirectUrls = "";    // https://www.google.com/a,https://www.google.com/b

                        if (TemptButtonRedirectUrl != null && HasVariables(TemptButtonRedirectUrl))
                        {
                            if (model.RedirectUrlVariableValues == null)
                            {
                                throw new Exception("RedirectUrlVariableValues is required");
                            }
                        }
                        int i = 1, j = 0;
                        foreach (var button in urlButtons)
                        {
                            if (i == 1)
                            {
                                if (HasVariables(button.ButtonValue))
                                {

                                    TemptButtonRedirectUrls = button.ButtonValue.Replace("{{1}}", model.RedirectUrlVariableValues[j]);


                                    j++;
                                }
                                else
                                {
                                    TemptButtonRedirectUrls = button.ButtonValue;
                                }
                                i++;
                                continue;
                            }
                            if (i == 2)
                            {
                                TemptButtonRedirectUrls = TemptButtonRedirectUrls + ",";

                                if (HasVariables(button.ButtonValue))
                                {


                                    TemptButtonRedirectUrls = TemptButtonRedirectUrls + button.ButtonValue.Replace("{{1}}", model.RedirectUrlVariableValues[j]);

                                }
                                else
                                {
                                    TemptButtonRedirectUrls = TemptButtonRedirectUrls + button.ButtonValue;
                                }
                            }
                        }

                        if (template != null && TempHeader != null && HasVariables(template.Header))
                        {
                            if (model.HeaderVariableValue != null)
                            {
                                TempHeader = ReplaceVariable(template.Header, new string[] { model.HeaderVariableValue });
                            }
                        }


                        if (template != null && TemptBody != null && HasVariables(TemptBody))
                        {
                            var replacedBody = template.Body;
                            var matches = Regex.Matches(replacedBody, @"\{\{(\d+)\}\}");
                            int index = matches.Count();
                            int a = 0;
                            foreach (Match match in matches)
                            {
                                if (index > 0 && index <= model.BodyVariableValues.Length)
                                {
                                    var bodyValueParts = model.BodyVariableValues[a].Split(',');
                                    if (index > 0)
                                    {
                                        replacedBody = replacedBody.Replace(match.Value, bodyValueParts[0].Trim());
                                    }
                                    a++;
                                }
                                index--;
                            }
                            TemptBody = replacedBody;
                        }

                        // send to meta json data
                        string modifiedHeader = template.Header;
                        string modifiedBody = template.Body;
                        string modifiedButtonsRedirectUrl = string.Join(",", urlButtons.Select(b => b.ButtonValue));

                        if (template != null && modifiedHeader != null && HasVariables(template.Header))
                        {
                            var match = Regex.Match(template.Header, @"\{\{\d+\}\}"); // Find the first match
                            if (match.Success)
                            {
                                modifiedHeader = model.HeaderVariableValue;
                            }
                        }

                        int i1 = 1, j1 = 0;
                        string modifiedButtonRedirectUrl1 = null;
                        string modifiedButtonRedirectUrl2 = null;
                        foreach (var button in urlButtons)
                        {
                            if (i1 == 1)
                            {
                                if (HasVariables(button.ButtonValue))
                                {
                                    modifiedButtonRedirectUrl1 = model.RedirectUrlVariableValues[j1];
                                    j1++;
                                }
                                else
                                {

                                }
                                i1++;
                                continue;
                            }
                            if (i1 == 2)
                            {
                                if (HasVariables(button.ButtonValue))
                                {
                                    modifiedButtonRedirectUrl2 = model.RedirectUrlVariableValues[j1];
                                }
                                else
                                {

                                }
                            }
                        }
                        if (modifiedButtonRedirectUrl1 == null)
                        {
                            modifiedButtonRedirectUrl1 = modifiedButtonRedirectUrl2;
                            modifiedButtonRedirectUrl2 = null;
                        }
                        if (template != null && modifiedBody != null && HasVariables(template.Body))
                        {
                            var parameters = new List<JObject>();
                            var matches = Regex.Matches(template.Body, @"\{\{\d+\}\}");
                            int index = matches.Count;
                            int a = 0;

                            foreach (Match match in matches)
                            {
                                if (index > 0 && index <= model.BodyVariableValues.Length)
                                {
                                    var bodyValueParts = model.BodyVariableValues[a].Split(',');
                                    var value = bodyValueParts[0].Trim();
                                    var parameter = new JObject
                                    {
                                        ["type"] = "text",
                                        ["text"] = value
                                    };
                                    parameters.Add(parameter);
                                    a++;
                                }
                                index--;
                            }
                            modifiedBody = JsonConvert.SerializeObject(parameters);
                        }

                        var Mediatype = template.MediaType.ToString().ToLower();

                        if (Mediatype == "none" || Mediatype == "text")
                        {
                            Mediatype = null;
                        }
                        var jsonData = $@"

                           {{
                                 ""messaging_product"": ""whatsapp"",
                                 ""recipient_type"": ""individual"",
                                 ""to"": ""{phoneNumberId}"",
                                 ""type"": ""template"",
                                 ""template"": {{
                                 ""name"": ""{template.TemplateName}"",
                                 ""language"": {{
                                 ""code"": ""{template.LanguageCode}""
                           }}";

                        // Check if MediaTypes is null (work in none && text template) where template,body are not null or empty, and they doesn't contain variables.
                        if (Mediatype == null && template != null && (!string.IsNullOrEmpty(template.Body) ||
                                                                              !string.IsNullOrEmpty(template.Header) ||
                                                                                      !string.IsNullOrEmpty(modifiedButtonsRedirectUrl)))
                        {
                            bool bodyContainsVariable = HasVariables(template.Body);
                            bool headerContainsVariable = template.Header != null ? HasVariables(template.Header) : false;
                            bool RedirectUrlsContainsVariable = modifiedButtonsRedirectUrl != null ? HasVariables(modifiedButtonsRedirectUrl) : false;

                            if (!bodyContainsVariable && !RedirectUrlsContainsVariable && !headerContainsVariable)
                            {
                                jsonData += $@"

                                     }}
                                  }}";

                            }
                        }

                        // Check if MediaTypes is not null (work in media template) where template,body are not null or empty, and they doesn't contain variables.
                        if (Mediatype != null && template != null && (!string.IsNullOrEmpty(template.Body) || !string.IsNullOrEmpty(modifiedButtonsRedirectUrl)))
                        {
                            bool bodyContainsVariable = HasVariables(template.Body);
                            bool RedirectUrlsContainsVariable = modifiedButtonsRedirectUrl != null ? HasVariables(modifiedButtonsRedirectUrl) : false;

                            if (!bodyContainsVariable && !RedirectUrlsContainsVariable)
                            {
                                jsonData += $@",
                                       ""components"": [
                                                        {{
                                                            ""type"": ""header"",
                                                                      ""parameters"": [
                                                                                       {{
                                                                                         ""type"": ""{Mediatype.ToLower()}"",
                                                                                         ""{Mediatype.ToLower()}"": {{
                                                                                                                      ""link"": ""{MadiaUrls}""
                                                                                                                     }}
                                                                                       }}
                                                                                      ]
                                                                                    }}
                                                        ]
                                                     }}
                                                }}";
                            }
                        }
                        // Check if MediaTypes is null (work in none template) where template,body are not null or empty, and they contain variables.
                        if (Mediatype == null && template != null && ((!string.IsNullOrEmpty(template.Body) && HasVariables(template.Body))
                                                                        || (!string.IsNullOrEmpty(modifiedButtonsRedirectUrl)
                                                                            && HasVariables(modifiedButtonsRedirectUrl)))
                                                                               && string.IsNullOrEmpty(template.Header))
                        {
                            bool bodyContainsVariable = HasVariables(template.Body);
                            bool RedirectUrlsContainsVariable = modifiedButtonsRedirectUrl != null ? HasVariables(modifiedButtonsRedirectUrl) : false;

                            jsonData += $@",
                                   ""components"": [";
                            bool firstComponent = true;

                            if (bodyContainsVariable)
                            {
                                jsonData += $@"
                                                {{
                                                  ""type"": ""body"",
                                                  ""parameters"": {modifiedBody}
                                                }}";

                                firstComponent = false;
                            }
                            int index = 0;
                            if (phoneNumberButtons != null)
                            {
                                index = 1;
                            }
                            if (RedirectUrlsContainsVariable)
                            {
                                if (!string.IsNullOrEmpty(modifiedButtonRedirectUrl1) && modifiedButtonRedirectUrl1.Trim() != "")
                                {
                                    if (!firstComponent)
                                    {
                                        jsonData += ",";
                                    }
                                    jsonData += $@"
                                                     {{
                                                          ""type"": ""button"",
                                                          ""sub_type"": ""url"",
                                                          ""index"": ""{index}"",
                                                          ""parameters"": [
                                                           {{
                                                              ""type"": ""PAYLOAD"",
                                                              ""payload"":""{modifiedButtonRedirectUrl1}""
                                                           }}
                                                         ]
                                                      }}";
                                    index++;

                                    if (!string.IsNullOrEmpty(modifiedButtonRedirectUrl2) && modifiedButtonRedirectUrl2.Trim() != "")
                                    {
                                        jsonData += $@",
                                                      {{
                                                          ""type"": ""button"",
                                                          ""sub_type"": ""url"",
                                                          ""index"": ""{index}"",
                                                          ""parameters"": [
                                                           {{
                                                              ""type"": ""PAYLOAD"",
                                                              ""payload"":""{modifiedButtonRedirectUrl2}""
                                                           }}
                                                         ]
                                                      }}";
                                    }
                                }
                            }
                            jsonData += $@"
                                     ]
                                   }}
                                }}";
                        }

                        // Check if MediaTypes is null (work in text template) and template, body, and header are not null or empty, and they contain variables.
                        if (Mediatype == null && template != null && (((!string.IsNullOrEmpty(template.Body) && HasVariables(template.Body)) ||
                                                                        (!string.IsNullOrEmpty(modifiedButtonsRedirectUrl) && HasVariables(modifiedButtonsRedirectUrl)) ||
                                                                          (!string.IsNullOrEmpty(template.Header) && HasVariables(template.Header)))) && !string.IsNullOrEmpty(template.Header))

                        {
                            bool bodyContainsVariable1 = HasVariables(template.Body);
                            bool headerContainsVariable1 = template.Header != null ? HasVariables(template.Header) : false;
                            bool RedirectUrlsContainsVariable1 = modifiedButtonsRedirectUrl != null ? HasVariables(modifiedButtonsRedirectUrl) : false;

                            jsonData += $@",
                                   ""components"": [";

                            bool firstComponent = true;
                            if (headerContainsVariable1)
                            {
                                if (!string.IsNullOrEmpty(modifiedHeader) && modifiedHeader.Trim() != "")
                                {
                                    jsonData += $@"
                                                         {{
                                                           ""type"": ""header"",
                                                           ""parameters"": [
                                                           {{
                                                             ""type"": ""text"",
                                                             ""text"": ""{modifiedHeader}""
                                                           }}
                                                         ]
                                                    }}";

                                    firstComponent = false;
                                }
                            }
                            if (bodyContainsVariable1)
                            {
                                if (!firstComponent)
                                {
                                    jsonData += ",";
                                }
                                jsonData += $@"
                                                        {{
                                                          ""type"": ""body"",
                                                          ""parameters"": {modifiedBody}
                                                        }}";

                                firstComponent = false;

                            }
                            int index = 0;
                            if (phoneNumberButtons != null)
                            {
                                index = 1;
                            }
                            if (RedirectUrlsContainsVariable1)
                            {
                                if (!string.IsNullOrEmpty(modifiedButtonRedirectUrl1) && modifiedButtonRedirectUrl1.Trim() != "")
                                {
                                    if (!firstComponent)
                                    {
                                        jsonData += ",";
                                    }
                                    jsonData += $@"
                                                             {{
                                                                  ""type"": ""button"",
                                                                  ""sub_type"": ""url"",
                                                                  ""index"": ""{index}"",
                                                                  ""parameters"": [
                                                                   {{
                                                                      ""type"": ""PAYLOAD"",
                                                                      ""payload"":""{modifiedButtonRedirectUrl1}""
                                                                   }}
                                                                 ]
                                                              }}";
                                    index++;

                                    if (!string.IsNullOrEmpty(modifiedButtonRedirectUrl2) && modifiedButtonRedirectUrl2.Trim() != "")
                                    {
                                        jsonData += $@",
                                                              {{
                                                                  ""type"": ""button"",
                                                                  ""sub_type"": ""url"",
                                                                  ""index"": ""{index}"",
                                                                  ""parameters"": [
                                                                   {{
                                                                      ""type"": ""PAYLOAD"",
                                                                      ""payload"":""{modifiedButtonRedirectUrl2}""
                                                                   }}
                                                                 ]
                                                              }}";

                                    }
                                }
                            }
                            jsonData += $@"

                               ]
                            }}
                         }}";

                        }

                        // Check if MediaTypes is not null (work in Media template) and template, body, and header are not null or empty, and they contain variables.
                        if (Mediatype != null && template != null && ((!string.IsNullOrEmpty(template.Body) && HasVariables(template.Body)) ||
                                                                        (!string.IsNullOrEmpty(modifiedButtonsRedirectUrl) && HasVariables(modifiedButtonsRedirectUrl))))

                        {
                            bool bodyContainsVariable2 = HasVariables(template.Body);
                            bool RedirectUrlsContainsVariable2 = modifiedButtonsRedirectUrl != null ? HasVariables(modifiedButtonsRedirectUrl) : false;
                            bool firstComponent = true;
                            jsonData += $@",
                                      ""components"": [
                                                      {{
                                                        ""type"": ""header"",
                                                                  ""parameters"": [
                                                                                  {{
                                                                                   ""type"": ""{Mediatype.ToLower()}"",
                                                                                   ""{Mediatype.ToLower()}"": {{
                                                                                                                ""link"": ""{MadiaUrls}""
                                                                                                               }}
                                                                                   }}
                                                                                  ]
                                                                              }}";


                            if (bodyContainsVariable2)
                            {
                                jsonData += $@",
                                                {{
                                                  ""type"": ""body"",
                                                  ""parameters"": {modifiedBody}
                                                }}";

                                firstComponent = false;
                            }

                            int index = 0;

                            if (phoneNumberButtons != null)
                            {
                                index = 1;
                            }

                            if (RedirectUrlsContainsVariable2)
                            {
                                if (!string.IsNullOrEmpty(modifiedButtonRedirectUrl1) && modifiedButtonRedirectUrl1.Trim() != "")
                                {
                                    if (!firstComponent)
                                    {
                                        jsonData += ",";
                                    }
                                    jsonData += $@"
                                                         {{
                                                              ""type"": ""button"",
                                                              ""sub_type"": ""url"",
                                                              ""index"": ""{index}"",
                                                              ""parameters"": [
                                                               {{
                                                                  ""type"": ""PAYLOAD"",
                                                                  ""payload"":""{modifiedButtonRedirectUrl1}""
                                                               }}
                                                             ]
                                                          }}";
                                    index++;

                                    if (!string.IsNullOrEmpty(modifiedButtonRedirectUrl2) && modifiedButtonRedirectUrl2.Trim() != "")
                                    {
                                        jsonData += $@",
                                                          {{
                                                              ""type"": ""button"",
                                                              ""sub_type"": ""url"",
                                                              ""index"": ""{index}"",
                                                              ""parameters"": [
                                                               {{
                                                                  ""type"": ""PAYLOAD"",
                                                                  ""payload"":""{modifiedButtonRedirectUrl2}""
                                                               }}
                                                             ]
                                                          }}";

                                    }
                                }
                            }
                            jsonData += $@"
                                       ]
                                    }}
                                }}";

                        }
                        JObject jsonObject = JObject.Parse(jsonData);
                        var request = new HttpRequestMessage(new HttpMethod("POST"), $"https://graph.facebook.com/v23.0/{Business.PhoneNumberID}/messages");
                        request.Content = new StringContent(jsonData, Encoding.UTF8, "application/json");
                        response = await _client.SendAsync(request);

                        if (response.IsSuccessStatusCode)
                        {
                            await using var stream = await response.Content.ReadAsStreamAsync();
                            using var reader = new StreamReader(stream);
                            using var jsonReader = new JsonTextReader(reader);
                            var result = _serializer.Deserialize<SendMessageResponseDto>(jsonReader);

                            EngagetoEntities.Enums.SendStatus status;
                            switch (result.Messages[0].Status)
                            {
                                case EngagetoEntities.Enums.SendStatus.Accepted:
                                    status = EngagetoEntities.Enums.SendStatus.Accepted;
                                    break;
                                case EngagetoEntities.Enums.SendStatus.Rejected:
                                    status = EngagetoEntities.Enums.SendStatus.Rejected;
                                    break;
                                default:
                                    // Log or debug the unexpected status value
                                    Console.WriteLine($"Unexpected status value: {result.Messages[0].Status}");
                                    status = EngagetoEntities.Enums.SendStatus.Accepted; // Assign a default status
                                    break;
                            }

                            Guid parsedBusinessId = Guid.TryParse(model.BusinessId, out var tempGuid) ? tempGuid : Guid.Empty;
                            Guid contactId = _context.Contacts.FirstOrDefault(i => i.BusinessId == parsedBusinessId && (i.CountryCode + i.Contact).Replace("+", "") == To.Replace("+", ""))?.ContactId ?? Guid.Empty;

                            // Save data to Conversations table
                            EngagetoEntities.Entities.Conversations sendTemplateEntity = new()
                            {
                                Id = Guid.NewGuid(),
                                TemplateMediaType = template.MediaType,
                                TemplateMediaUrl = template.MediaAwsUrl,
                                CreatedAt = DateTime.UtcNow,
                                TemplateBody = TemptBody,
                                TemplateHeader = TempHeader,
                                TemplateFooter = template.Footer,
                                CallButtonName = phoneNumberButtons == null ? "" : phoneNumberButtons.ButtonName,
                                PhoneNumber = phoneNumberButtons == null ? "" : phoneNumberButtons.ButtonValue,
                                UrlButtonNames = String.Join(",", urlButtons.Select(m => m.ButtonName)),
                                RedirectUrls = TemptButtonRedirectUrls,
                                QuickReplies = String.Join(",", quickReplyButtons.Select(m => m.ButtonValue)),
                                ContactId = contactId,
                                BusinessId = parsedBusinessId
                            };
                            if (result.Contacts != null && result.Contacts.Count > 0 && result.Messages != null && result.Messages.Count > 0)
                            {
                                sendTemplateEntity.To = To;
                                sendTemplateEntity.From = Business.PhoneNumberID;
                                sendTemplateEntity.WhatsAppMessageId = result.Messages[0].WhatsAppMessageId;
                                sendTemplateEntity.Status = ConvStatus.sent;
                            }
                            try
                            {
                                _context.Conversations.Add(sendTemplateEntity);
                                await _context.SaveChangesAsync();
                            }
                            catch (Exception ex)
                            {
                                throw new Exception(ex.ToString());
                            }
                        }
                    }
                }
            }
            return response;
        }
        public async Task MarkTemplateAsDeletedAsync(DeleteTemplateDto model)
        {
            var Business = await _context.BusinessDetailsMetas.FirstOrDefaultAsync(t => t.BusinessId == model.BusinessId);
            var user = _context.Users.FirstOrDefault(u => u.Id == model.UserId);
            if (Business == null && user == null) throw new UnauthorizedAccessException("Invalid businessId or userId");

            var template = await _context.Templates.FirstOrDefaultAsync(t => t.TemplateId == model.TemplateId);
            _client.DefaultRequestHeaders.Add("Authorization", "Bearer " + Business?.Token);
            if (template.IsDeleted) throw new Exception("Template is already marked as deleted.");
            string baseUrl = MetaApi.GetBaseUrl();
            var request = new HttpRequestMessage(HttpMethod.Delete, $"{baseUrl}{Business.WhatsAppBusinessAccountID}/message_templates?name={template.TemplateName}");
            var response = await _client.SendAsync(request);
            if (response.IsSuccessStatusCode)
            {
                template.IsDeleted = true;
                template.UpdatedDate = DateTime.UtcNow;
                template.Status = WATemplateStatus.DELETED;
                await _context.SaveChangesAsync();
            }
            else
            {
                throw new Exception($"Failed to mark template as deleted. Facebook API response: {response.StatusCode}");
            }
        }

        public List<Object> GetTemplateByIdAsync(List<Guid> ids, string businessId, Guid userId)
        {
            var business = _context.BusinessDetailsMetas.FirstOrDefault(t => t.BusinessId == businessId);
            var user = _context.Users.FirstOrDefault(u => u.Id == userId);
            if (business == null || user == null) throw new Exception("Business or User with the specified ID does not exist.");

            var normalTemplates = _context.Templates.Where(t => ids.Contains(t.TemplateId) && t.MediaType != MediaType.CAROUSEL).ToList();
            var carouselTemplates = _context.Templates.Where(t => ids.Contains(t.TemplateId) && t.MediaType == MediaType.CAROUSEL).ToList();

            var buttons = _context.ButtonDetails.Where(b => ids.Contains(b.TemplateId)).ToList();
            var CountryCode = _context.CountryDetails.Select(m => m.CountryCode).ToList();
            List<Object> templates = new List<Object>();
            if (ids == null) throw new Exception("Template ids not provided");
            // Map templates to TemplateDto
            if (carouselTemplates != null && carouselTemplates.Count > 0)
            {
                foreach (var item in carouselTemplates)
                {
                    List<CarouselCardsDto> carouselCards = new List<CarouselCardsDto>();

                    try
                    {
                        if (!string.IsNullOrWhiteSpace(item.CarouselCardsJson))
                        {
                            List<string> jsonStrings = JsonConvert.DeserializeObject<List<string>>(item.CarouselCardsJson) ?? new();

                            foreach (var jsonString in jsonStrings)
                            {
                                var card = JsonConvert.DeserializeObject<CarouselCardsDto>(jsonString);
                                carouselCards.Add(card);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error deserializing CarouselCardsJson: {ex.Message}");
                    }

                    var dto = new
                    {
                        TemplateId = item.TemplateId,
                        UserId = item.UserId,
                        BusinessId = item.BusinessId,
                        TemplateName = item.TemplateName,
                        Category = item.Category,
                        SubCategory = item.SubCategory,
                        MediaType = item.MediaType,
                        mediaFile = item.MediaAwsUrl,
                        Header = item.Header?.Replace("\\\"", "\"").Replace("\\n", "\n"),
                        LanguageCode = item.LanguageCode,
                        Body = item.Body.Replace("\\\"", "\"").Replace("\\n", "\n"),
                        Footer = item.Footer?.Replace("\\\"", "\"").Replace("\\n", "\n"),
                        Status = item.Status,
                        Createdby = item.Createdby,
                        CreatedDate = item.CreatedDate,
                        UpdatedDate = item.UpdatedDate,
                        CarouselCards = carouselCards
                    };
                    templates.Add(dto);
                }
            }
            else
            {
                foreach (var item in normalTemplates)
                {
                    var Buttons = buttons.Where(m => m.TemplateId == item.TemplateId).ToList();
                    var buttonObjects = new List<Object>();

                    foreach (var button in Buttons)
                    {
                        string countryCode = null;
                        string buttonValue = button.ButtonValue.Trim();
                        if (button.ButtonType == "PHONE_NUMBER")
                        {
                            buttonValue = "+" + buttonValue;
                            foreach (var code in CountryCode)
                            {

                                if (buttonValue.StartsWith(code.Trim()))
                                {
                                    countryCode = code;
                                    // Check if the number has a space
                                    if (button.ButtonValue.Contains(" "))
                                    {
                                        countryCode += " ";
                                    }
                                    // Preserve the exact format of the country code (including any space)
                                    buttonValue = buttonValue.Substring(code.Trim().Length).TrimStart();
                                    break;
                                }
                            }
                        }
                        var Obj = new
                        {
                            Id = button.Id,
                            ButtonType = button.ButtonType,
                            ButtonName = button.ButtonName,
                            ButtonValue = buttonValue,
                            CountryCode = countryCode
                        };
                        buttonObjects.Add(Obj);
                    }
                    var dto = new
                    {
                        TemplateId = item.TemplateId,
                        UserId = item.UserId,
                        BusinessId = item.BusinessId,
                        TemplateName = item.TemplateName,
                        Category = item.Category,
                        SubCategory = item.SubCategory,
                        MediaType = item.MediaType,
                        mediaFile = item.MediaAwsUrl,
                        Header = item.Header?.Replace("\\\"", "\"").Replace("\\n", "\n"),
                        LanguageCode = item.LanguageCode,
                        Body = item.Body.Replace("\\\"", "\"").Replace("\\n", "\n"),
                        Footer = item.Footer?.Replace("\\\"", "\"").Replace("\\n", "\n"),
                        Status = item.Status,
                        Createdby = item.Createdby,
                        CreatedDate = item.CreatedDate,
                        UpdatedDate = item.UpdatedDate,
                        Buttons = buttonObjects,
                        AuthTemplatePreviewJson = item.AuthTemplatePreviewJson
                    };
                    templates.Add(dto);
                }
            }
            return templates;
        }

        // For filters

        public List<Template> ApplyCondition(List<Template> list, TemplateFilterCondition condition, PropertyInfo property)
        {
            if (property != null)
            {
                if (property.PropertyType == typeof(string))
                {
                    switch (condition.Operator.ToLower())
                    {
                        case "equal":
                            list = list.Where(item => property.GetValue(item).Equals(condition.Value)).ToList();
                            break;

                        case "notequal":
                            list = list.Where(item => !property.GetValue(item).Equals(condition.Value)).ToList();
                            break;

                        case "contains":
                            list = list.Where(item => ((string)property.GetValue(item)).Contains(condition.Value)).ToList();
                            break;

                        case "startswith":
                            list = list.Where(item => ((string)property.GetValue(item)).StartsWith(condition.Value)).ToList();
                            break;

                        case "endswith":
                            list = list.Where(item => ((string)property.GetValue(item)).EndsWith(condition.Value)).ToList();
                            break;
                        default:
                            // Handle unsupported operator
                            break;
                    }
                }
                else if (property.PropertyType == typeof(DateTime?))
                {
                    if (DateTime.TryParseExact(condition.Value, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime dateTimeValue))
                    {
                        switch (condition.Operator.ToLower())
                        {
                            case "equal":
                                list = list.Where(item =>
                                {
                                    var value = (DateTime?)property.GetValue(item);
                                    return value.HasValue && value.Value.Equals(dateTimeValue);
                                }).ToList();
                                break;

                            case "notequal":
                                list = list.Where(item =>
                                {
                                    var value = (DateTime?)property.GetValue(item);
                                    return !value.HasValue || !value.Value.Equals(dateTimeValue);
                                }).ToList();
                                break;

                            case "after":
                                list = list.Where(item => ((DateTime?)property.GetValue(item))?.CompareTo(dateTimeValue) > 0).ToList();
                                break;

                            case "before":
                                list = list.Where(item => ((DateTime?)property.GetValue(item))?.CompareTo(dateTimeValue) < 0).ToList();
                                break;
                            // Add more date-based operators as needed
                            default:
                                // Handle unsupported operator
                                break;
                        }
                    }
                }
                else if (property.PropertyType == typeof(MediaType))
                {
                    if (int.TryParse(condition.Value, out int intValue))
                    {
                        switch (condition.Operator.ToLower())
                        {
                            case "equal":
                                list = list.Where(item =>
                                {
                                    var value = property.GetValue(item);
                                    return value != null && ((int)value).Equals(intValue);
                                }).ToList();
                                break;

                            case "notequal":
                                list = list.Where(item =>
                                {
                                    var value = property.GetValue(item);
                                    return value == null || !((int)value).Equals(intValue);
                                }).ToList();
                                break;

                            case "greaterthan":
                                list = list.Where(item =>
                                {
                                    var value = property.GetValue(item);
                                    return value != null && ((int)value) > intValue;
                                }).ToList();
                                break;

                            case "lessthan":
                                list = list.Where(item =>
                                {
                                    var value = property.GetValue(item);
                                    return value != null && ((int)value) < intValue;
                                }).ToList();
                                break;
                            // Add more int-based operators as needed
                            default:
                                // Handle unsupported operator
                                break;
                        }
                    }
                }
                else if (property.PropertyType == typeof(WATemplateStatus?))
                {
                    if (int.TryParse(condition.Value, out int intValue))
                    {
                        switch (condition.Operator.ToLower())
                        {
                            case "equal":
                                list = list.Where(item =>
                                {
                                    var value = property.GetValue(item);
                                    return value != null && ((int)value).Equals(intValue);
                                }).ToList();
                                break;

                            case "notequal":
                                list = list.Where(item =>
                                {
                                    var value = property.GetValue(item);
                                    return value == null || !((int)value).Equals(intValue);
                                }).ToList();
                                break;

                            case "greaterthan":
                                list = list.Where(item =>
                                {
                                    var value = property.GetValue(item);
                                    return value != null && ((int)value) > intValue;
                                }).ToList();
                                break;

                            case "lessthan":
                                list = list.Where(item =>
                                {
                                    var value = property.GetValue(item);
                                    return value != null && ((int)value) < intValue;
                                }).ToList();
                                break;
                            // Add more int-based operators as needed
                            default:
                                // Handle unsupported operator
                                break;
                        }
                    }
                }

                else if (property.PropertyType == typeof(WATemplateCategory?))
                {
                    if (int.TryParse(condition.Value, out int intValue))
                    {
                        switch (condition.Operator.ToLower())
                        {
                            case "equal":
                                list = list.Where(item =>
                                {
                                    var value = property.GetValue(item);
                                    return value != null && ((int)value).Equals(intValue);
                                }).ToList();
                                break;

                            case "notequal":
                                list = list.Where(item =>
                                {
                                    var value = property.GetValue(item);
                                    return value == null || !((int)value).Equals(intValue);
                                }).ToList();
                                break;

                            case "greaterthan":
                                list = list.Where(item =>
                                {
                                    var value = property.GetValue(item);
                                    return value != null && ((int)value) > intValue;
                                }).ToList();
                                break;

                            case "lessthan":
                                list = list.Where(item =>
                                {
                                    var value = property.GetValue(item);
                                    return value != null && ((int)value) < intValue;
                                }).ToList();
                                break;
                            // Add more int-based operators as needed
                            default:
                                // Handle unsupported operator
                                break;
                        }
                    }
                }
            }
            return list;
        }

        public List<Template> ApplyFilter(List<Template> list, TemplateFilterGroup filtering)
        {
            if (filtering != null && filtering.FilterType != null && filtering.FilterType.Any())
            {
                List<Template> filteredList = new List<Template>();

                if (filtering.FilterType == "and")
                {
                    filteredList = list;
                    foreach (var condition in filtering.Conditions)
                    {
                        var property = typeof(Template).GetProperty(condition.Column, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                        if (property != null)
                        {
                            filteredList = ApplyCondition(filteredList, condition, property);
                        }
                    }
                }
                else if (filtering.FilterType == "or")
                {
                    foreach (var condition in filtering.Conditions)
                    {
                        var property = typeof(Template).GetProperty(condition.Column, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                        if (property != null)
                        {
                            var tempFilteredList = ApplyCondition(list, condition, property);
                            foreach (var item in tempFilteredList)
                            {
                                if (!filteredList.Contains(item))
                                {
                                    filteredList.Add(item);
                                }
                            }
                        }
                    }
                }
                list = filteredList;
            }
            return list;
        }

        public async Task<EngagetoEntities.Entities.Conversations> SaveConversationAsync(EngagetoEntities.Entities.Conversations conversation)
        {
            _context.Conversations.Add(conversation);
            await _context.SaveChangesAsync();
            return conversation;
        }
        public async Task<Conversations> GetSendTemplateAsync(string companyId, Guid businessId, TemplateRequestDto templateRequestDto)
        {
            try
            {
                var source = await _context.ApiKeyEntities.Where(i => i.CompanyId == companyId).Select(i => i.Source).FirstOrDefaultAsync();
                await _conversationsService.VerifySubscriptionAndExpectedWalletBalanceAysc(companyId, 1);
                if (string.IsNullOrEmpty(templateRequestDto.CountryCode))
                {
                    templateRequestDto.CountryCode = await _companyDetailsService.GetCompanyCountryCode(businessId) ?? "+91";
                }

                if (templateRequestDto.HeaderValue == "default")
                    templateRequestDto.HeaderValue = null;

                if (templateRequestDto.BodyVariableValues?.Contains("undefined") ?? new())
                    templateRequestDto.BodyVariableValues?.Remove("undefined");

                var template = await _context.Templates.FirstOrDefaultAsync(t => t.BusinessId == companyId
                   && (t.TemplateName == templateRequestDto.TemplateName))
                    ?? throw new NotFoundException("Template not found.");
                template.Buttons = await _context.ButtonDetails.Where(x => x.TemplateId == template.TemplateId).ToListAsync();

                EngagetoEntities.Entities.BusinessDetailsMeta business = await _context.BusinessDetailsMetas.FirstOrDefaultAsync(t => !string.IsNullOrEmpty(companyId) && t.BusinessId == companyId);

                var jsonObject = new JObject();

                template.Body = StringHelper.ReplaceAndExtractVariables(template.Body ?? string.Empty).UpdatedMessage;
                if (template.Status != WATemplateStatus.APPROVED)
                    throw new Exception("Template is not approved.");

                if (template.SendTemplateJsonDto != null)
                {
                    jsonObject = ProcessTemplateJson(template.SendTemplateJsonDto, templateRequestDto.Contact, templateRequestDto.HeaderValue, templateRequestDto.BodyVariableValues);
                }
                else
                {
                    jsonObject = ProcessTemplateComponents(template, templateRequestDto.Contact, templateRequestDto.HeaderValue, templateRequestDto.BodyVariableValues);
                }

                var url = MetaApi.GetSendTemplateUrl(business.PhoneNumberID);
                var response = await TemplateMetaApiAsync<SendMessageResponseDto>(url, jsonObject, business.Token);
                var conversation = await SaveConversationAsync(template, response, companyId, templateRequestDto, source);
                return conversation;

            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<JObject> GetWhatsAppSendTemplatePayloadAsync(string companyId,
           Guid? templateId,
           string? templateName,
           string contact,
           string? headerVariableValue,
           List<string>? bodyVariableValue)
        {
            try
            {
                //var template = await _context.Templates.FirstOrDefaultAsync(t => t.BusinessId == companyId
                //   && (t.TemplateId == templateId || t.TemplateName == templateName))
                //    ?? throw new NotFoundException("Template not found.");

                var template = await _context.Templates.FirstOrDefaultAsync(t => t.BusinessId == companyId && t.TemplateId == templateId)
                               ?? (!string.IsNullOrEmpty(templateName) ? await _context.Templates.FirstOrDefaultAsync(t =>
                               t.BusinessId == companyId && t.TemplateName == templateName) : null)
                               ?? throw new NotFoundException("Template not found.");




                template.Body = StringHelper.ReplaceAndExtractVariables(template.Body ?? string.Empty).UpdatedMessage;
                if (template.Status != WATemplateStatus.APPROVED)
                    throw new Exception("Template is not approved.");

                if (template.SendTemplateJsonDto != null)
                {
                    return ProcessTemplateJson(template.SendTemplateJsonDto, contact, headerVariableValue, bodyVariableValue);
                }
                else
                {
                    return ProcessTemplateComponents(template, contact, headerVariableValue, bodyVariableValue);
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        private JObject ProcessTemplateJson(string templateJsonText, string contact, string? headerVariableValue, List<string>? bodyVariableValue)
        {
            templateJsonText = templateJsonText.Replace("#Contact#", contact);

            if (templateJsonText.Contains("#HeaderValue#"))
            {
                if (headerVariableValue == null)
                    throw new Exception("Header variable value is required.");

                templateJsonText = templateJsonText.Replace("#HeaderValue#", headerVariableValue);
            }

            int bodyCountValue = CountOccurrencesStartingWith(templateJsonText);
            if (bodyCountValue > 0 && bodyCountValue != bodyVariableValue?.Count)
                throw new Exception("Body variable value required based on template variable.");

            for (int i = 0; i < bodyCountValue; i++)
            {
                templateJsonText = ReplaceFirstOccurrence(templateJsonText, $"#Sample{i + 1}#", bodyVariableValue[i]);
            }

            return JObject.Parse(templateJsonText);
        }

        private JObject ProcessTemplateComponents(EngagetoEntities.Entities.Template template, string contact, string? headerVariableValue, List<string>? bodyVariableValue)
        {
            JObject jsonObject = _metaPayloadService.InitializeBasePayload(contact, template.TemplateName, template.LanguageCode);
            var components = _metaPayloadService.GetComponents();

            int headerCount = StringHelper.GetVariableCount(template?.Header ?? string.Empty);
            if (template?.MediaType == MediaType.TEXT && headerCount > 0 && headerVariableValue == null)
            {
                throw new Exception("Header variable value is required.");
            }
            else if (template?.MediaType != MediaType.NONE)
            {
                if (string.IsNullOrEmpty(headerVariableValue))
                {
                    headerVariableValue = template.MediaAwsUrl;
                }
            }
            int bodyCount = StringHelper.GetVariableCount(template?.Body ?? string.Empty);
            if (bodyCount > 0 && (bodyVariableValue == null || bodyVariableValue.Count != bodyCount))
                throw new Exception("Body variable value required based on template variable.");

            _metaPayloadService.AddBodyComponent(ref components, template?.Body ?? string.Empty, bodyVariableValue);
            _metaPayloadService.AddHeaderComponent(ref components, template?.Header ?? string.Empty, template.MediaType, headerVariableValue);
            _metaPayloadService.AddButtonComponent(ref components, template.Buttons, new());

            if (jsonObject["template"] is JObject templateObject)
            {
                templateObject["components"] = components;
            }
            return jsonObject;
        }

        private static int CountOccurrencesStartingWith(string text)
        {
            int count = 0;
            int index = 0;

            while ((index = text.IndexOf($"#Sample{count + 1}#", index, StringComparison.OrdinalIgnoreCase)) != -1)
            {
                count++;
                index += $"#Sample{count}#".Length;
            }

            return count;
        }


        private static string ReplaceFirstOccurrence(string text, string searchText, string replaceText)
        {
            int pos = text.IndexOf(searchText, StringComparison.InvariantCultureIgnoreCase);
            if (pos < 0)
            {
                return text;
            }
            return text.Substring(0, pos) + replaceText + text.Substring(pos + searchText.Length);
        }

        public async Task<List<object>> GetApprovedTemplates(string CompanyId, string? TemplateName)
        {
            await _conversationsService.VerifySubscriptionAndExpectedWalletBalanceAysc(CompanyId);
            var templateQuery = _context.Templates.Where(t => t.Status == WATemplateStatus.APPROVED && t.BusinessId == CompanyId);

            if (!string.IsNullOrEmpty(TemplateName))
            {
                templateQuery = templateQuery.Where(t => t.TemplateName == TemplateName);
            }
            var templateList = await templateQuery.ToListAsync();

            var normalTemplates = await templateQuery.Where(t => t.MediaType != MediaType.CAROUSEL).ToListAsync();
            var carouselTemplates = await templateQuery.Where(t => t.MediaType == MediaType.CAROUSEL).ToListAsync();

            var templateIds = templateList.Select(t => t.TemplateId).ToList();

            var buttons = await _context.ButtonDetails.Where(b => templateIds.Contains(b.TemplateId)).ToListAsync();

            var countryCodes = await _context.CountryDetails.Select(m => m.CountryCode).ToListAsync();

            List<object> templates = new List<object>();
            if (carouselTemplates != null && carouselTemplates.Count > 0)
            {
                foreach (var item in carouselTemplates)
                {
                    List<CarouselCardsDto> carouselCards = new List<CarouselCardsDto>();

                    try
                    {
                        if (!string.IsNullOrWhiteSpace(item.CarouselCardsJson))
                        {
                            List<string> jsonStrings = JsonConvert.DeserializeObject<List<string>>(item.CarouselCardsJson) ?? new();

                            foreach (var jsonString in jsonStrings)
                            {
                                var card = JsonConvert.DeserializeObject<CarouselCardsDto>(jsonString);
                                carouselCards.Add(card);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error deserializing CarouselCardsJson: {ex.Message}");
                    }

                    var dto = new
                    {
                        TemplateId = item.TemplateId,
                        UserId = item.UserId,
                        BusinessId = item.BusinessId,
                        TemplateName = item.TemplateName,
                        Category = item.Category,
                        SubCategory = item.SubCategory,
                        MediaType = item.MediaType,
                        mediaFile = item.MediaAwsUrl,
                        Header = item.Header?.Replace("\\\"", "\"").Replace("\\n", "\n"),
                        LanguageCode = item.LanguageCode,
                        Body = item.Body.Replace("\\\"", "\"").Replace("\\n", "\n"),
                        Footer = item.Footer?.Replace("\\\"", "\"").Replace("\\n", "\n"),
                        Status = item.Status,
                        Createdby = item.Createdby,
                        CreatedDate = item.CreatedDate,
                        UpdatedDate = item.UpdatedDate,
                        CarouselCards = carouselCards
                    };
                    templates.Add(dto);
                }

            }
            else
            {
                foreach (var item in normalTemplates)
                {
                    var buttonObjects = buttons.Where(b => b.TemplateId == item.TemplateId)

                        .Select(button =>
                        {
                            string countryCode = null;
                            string buttonValue = button.ButtonValue.Trim();

                            if (button.ButtonType == "PHONE_NUMBER")
                            {
                                buttonValue = "+" + buttonValue;
                                foreach (var code in countryCodes)
                                {
                                    if (buttonValue.StartsWith(code.Trim()))
                                    {
                                        countryCode = code;
                                        if (button.ButtonValue.Contains(" "))
                                        {
                                            countryCode += " ";
                                        }
                                        buttonValue = buttonValue.Substring(code.Trim().Length).TrimStart();
                                        break;
                                    }
                                }
                            }
                            return new
                            {
                                Id = button.Id,
                                ButtonType = button.ButtonType,
                                ButtonName = button.ButtonName,
                                ButtonValue = buttonValue,
                                CountryCode = countryCode
                            };

                        }).ToList();

                    // Create template DTO object
                    var dto = new
                    {
                        TemplateId = item.TemplateId,
                        UserId = item.UserId,
                        BusinessId = item.BusinessId,
                        TemplateName = item.TemplateName,
                        Category = item.Category,
                        SubCategory = item.SubCategory,
                        MediaType = item.MediaType,
                        MediaFile = item.MediaAwsUrl,
                        Header = item.Header?.Replace("\\\"", "\"").Replace("\\n", "\n"),
                        LanguageCode = item.LanguageCode,
                        Body = item.Body.Replace("\\\"", "\"").Replace("\\n", "\n"),
                        Footer = item.Footer?.Replace("\\\"", "\"").Replace("\\n", "\n"),
                        Status = item.Status,
                        CreatedBy = item.Createdby,
                        CreatedDate = item.CreatedDate,
                        UpdatedDate = item.UpdatedDate,
                        Buttons = buttonObjects
                    };

                    templates.Add(dto);
                }
            }
            return templates;
        }

        public JObject GetAuthTemplatePayloadAsync(AuthTemplateDto templateDto)
        {
            try
            {
                var basePayload = CreateMetaPayloadJsonDto.CreateAuthBasePayload(templateDto.Name, templateDto.Languages.Select(x => x.ToString()).ToList(), templateDto.Category.ToString(), templateDto.MessageSendTtlSeconds);
                var components = new JArray();

                var bodyJobject = JObject.Parse(CreateMetaPayloadJsonDto.CreateAuthBodyPayload(templateDto.AddSecurityRecommendation));
                components.Add(bodyJobject);
                if (templateDto.CodeExpirationMinutes > 0)
                {
                    var footerJobject = JObject.Parse(CreateMetaPayloadJsonDto.CreateAuthFooter(templateDto.CodeExpirationMinutes));
                    components.Add(footerJobject);
                }
                if ((templateDto.OtpType == OtpType.ONE_TAP || templateDto.OtpType == OtpType.ZERO_TAP))
                {
                    if (templateDto.SupportedApps?.Any() == false || templateDto.SupportedApps?.Count > 5)
                        throw new Exception("Only 5 or fewer supported apps can be added.");

                    var buttonJobject = CreateMetaPayloadJsonDto.CreateAuthButton(templateDto.OtpType.ToString(), new JArray(templateDto.SupportedApps ?? new()));
                    components.Add(buttonJobject);

                }
                else if (OtpType.COPY_CODE == templateDto.OtpType)
                {
                    var buttonJobject = CreateMetaPayloadJsonDto.CreateAuthButton(templateDto.OtpType.ToString(), null);
                    components.Add(buttonJobject);
                }
                basePayload["components"] = components;

                return basePayload ?? new();
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<bool> ProcessAuthTemplatePayloadAsync(AuthTemplateDto dto, string businessId, string userName, Guid currentUserId)
        {
            var payload = GetAuthTemplatePayloadAsync(dto);
            var result = await _metaApiService.CreateAuthTemplateRequestAsync(businessId, payload);

            var template = new Template()
            {
                TemplateId = dto.Id ?? Guid.Empty,
                UserId = currentUserId,
                TemplateName = dto.Name,
                LanguageCode = string.Join(",", dto.Languages),
                Body = dto.Body,
                Footer = dto.Footer,
                BusinessId = businessId,
                Category = dto.Category,
                MediaType = MediaType.NONE,
                SubCategory = dto.SubCategory,
                Createdby = userName,
                Updatedby = userName,
                CreatedDate = DateTime.UtcNow,
                UpdatedDate = DateTime.UtcNow,
                AuthTemplatePreviewJson = JsonConvert.SerializeObject(dto)
            };
            if (result.Success)
            {
                var data = result.Result?["data"]?[0];
                var status = data?["status"];
                var id = data?["id"];
                if (status?.ToString().ToLowerInvariant() == "approved")
                {
                    template.Status = WATemplateStatus.APPROVED;
                }
                else if (status?.ToString().ToLowerInvariant() == "rejected")
                {
                    template.Status = WATemplateStatus.REJECTED;
                }
                else
                {
                    template.Status = WATemplateStatus.PENDING;
                }

                template.MetaId = id?.ToString() ?? string.Empty;
                await SaveTemplateAsync(template, currentUserId);

                if (dto.OtpType == OtpType.COPY_CODE || dto.OtpType == OtpType.ONE_TAP)
                {
                    Button buttonDetailDto = new Button() { ButtonType = "COPY_CODE", TemplateId = template.TemplateId };
                    if (dto.OtpType == OtpType.COPY_CODE)
                    {
                        buttonDetailDto.ButtonValue = dto.Button?.Text ?? "Copy code";
                    }
                    else if (dto.OtpType == OtpType.ONE_TAP)
                    {
                        buttonDetailDto.ButtonValue = dto.Button?.AutofillText ?? "Autofill";
                    }
                    await SaveButtonsAsync(new List<Button> { buttonDetailDto }, template.TemplateId);
                    template.Buttons = new List<Button> { buttonDetailDto };
                    return true;
                }
            }
            var error = result.Result?["error"]?["message"];
            throw new Exception(error?.ToString());
        }

        public async Task<Conversations> SendAuthTemplateServiceAsync(string businessId, SendAuthTemplateDto sendAuthTemplateDto)
        {
            try
            {

                var contact = await SaveContactAsync(sendAuthTemplateDto, businessId);
                var number = PhoneNumberHelper.GetCountryCodeAndPhoneNumber(sendAuthTemplateDto.MobileNumber);

                var conv = new Conversations()
                {
                    From = businessId,
                    To = number.CountryCode + number.NationalNumber,
                    BusinessId = Guid.TryParse(businessId, out var tempGuid) ? tempGuid : Guid.Empty,
                    ContactId = contact.ContactId,
                    CreatedAt = DateTime.UtcNow,
                    Id = Guid.NewGuid(),
                    MessageType = EngagetoEntities.Enums.MessageType.Template
                };
                var template = await _context.Templates.FirstOrDefaultAsync(x => x.BusinessId == businessId
                    && x.TemplateName == sendAuthTemplateDto.TemplateName)

                    ?? throw new InvalidOperationException("Not found template");

                var payload = MetaPayloadDto.SendAuthPayload(sendAuthTemplateDto.MobileNumber, sendAuthTemplateDto.OTP, template.TemplateName, template.LanguageCode);

                List<string> otp = new List<string>();
                otp.Add(sendAuthTemplateDto.OTP);
                conv.TemplateBody = StringHelper.ReplaceVariable(template.Body, otp.ToArray());
                conv.TemplateFooter = template.Footer;

                var authTemplatePreView = JsonConvert.DeserializeObject<AuthTemplateDto>(template.AuthTemplatePreviewJson ?? string.Empty);
                conv.UrlButtonNames = authTemplatePreView?.Button?.Text;
                conv.RedirectUrls = $"https://api.whatsapp.com/send?phone={sendAuthTemplateDto.MobileNumber}&text={sendAuthTemplateDto.OTP}";
                conv.ReferenceId = template.TemplateId.ToString();

                var result = await _metaApiService.SendAuthTemplateRequestAsync(businessId, JObject.FromObject(payload));
                if (!result.Success)
                {
                    conv.Status = EngagetoEntities.Enums.ConvStatus.failed;
                    conv.ErrorMessage = string.Empty;
                    conv.WhatsAppMessageId = string.Empty;
                    conv.ErrorDetails = JsonConvert.SerializeObject(result.Result);
                }
                conv.Status = EngagetoEntities.Enums.ConvStatus.sent;
                conv.WhatsAppMessageId = result.Result?["messages"]?[0]?["id"]?.ToString() ?? string.Empty;
                await _context.AddAsync(conv);
                await _context.SaveChangesAsync();
                return conv;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<bool> RestoredDeletedTemplate(RestoredTemplateDto model)
        {
            var Business = await _context.BusinessDetailsMetas.FirstOrDefaultAsync(t => t.BusinessId == model.BusinessId);
            var user = _context.Users.FirstOrDefault(u => u.Id == model.UserId);
            if (Business == null && user == null) throw new UnauthorizedAccessException("Invalid businessId or userId");
            var template = await _context.Templates.FirstOrDefaultAsync(t => t.TemplateId == model.TemplateId);
            if (template.IsDeleted)
            {
                template.IsDeleted = false;
                template.UpdatedDate = DateTime.UtcNow;
                await _context.SaveChangesAsync();
            }
            else
            {
                throw new Exception("Template is not a deleted.");
            }
            try
            {
                var matchingTemplate = await _metaApiService.GetTemplateByNameAsync(model.BusinessId, template.TemplateName);
                var templateStatus = matchingTemplate["status"]?.ToString();

                switch (templateStatus.ToLower())
                {
                    case "approved":
                        template.Status = WATemplateStatus.APPROVED;
                        break;
                    case "pending":
                        template.Status = WATemplateStatus.PENDING;
                        break;
                    case "rejected":
                        template.Status = WATemplateStatus.REJECTED;
                        break;

                    default:
                        template.Status = WATemplateStatus.DRAFT;
                        break;
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                throw ex;
            }

        }

        public async Task<HttpResponseMessage> CreateCarouselTemplateAsync(CarouselTemplateDto model, bool Draft)
        {
            var template = _context.Templates.FirstOrDefault(t => t.TemplateId == model.TemplateId);
            if (template != null)

            {
                var editTemplate = model.Adapt<EditTemplateDto>();
                var validResult = await _templateValidation.ValidateEditTemplate(editTemplate);
                if (validResult != null)
                {
                    throw new Exception($"{validResult}");
                }

            }
            else
            {
                var validationResult = await _templateValidation.ValidateCreateTemplate(model.Adapt<CreateTemplateDto>());
                if (validationResult != null)
                {
                    throw new Exception($"{validationResult}");
                }
                var mediaValidationResult = await _templateValidation.ValidateCreateMediaFile(model.Adapt<CreateTemplateDto>());
                if (mediaValidationResult != null)
                {
                    throw new Exception($"{validationResult}");
                }
            }
            var DraftTemplate = _context.Templates.Where(t => t.TemplateId == model.TemplateId && t.Status == WATemplateStatus.DRAFT).ToList();
            model.Body = StringHelper.FormateTemplateComponents(model.Body);
            var bodyMessage = model.Body;
            model.Body = StringHelper.ReplaceAndExtractVariables(bodyMessage).UpdatedMessage;
            model.Header = !string.IsNullOrEmpty(model.Header) ? StringHelper.FormateTemplateComponents(model.Header) : null;
            var headerMessage = model.Header;
            if (!string.IsNullOrEmpty(headerMessage))
            {
                model.Header = StringHelper.ReplaceAndExtractVariables(headerMessage).UpdatedMessage;
            }
            string HeaderHandle = null;
            HttpResponseMessage response = new HttpResponseMessage();
            var MediaTypes = Enum.GetName(typeof(EngagetoEntities.Enums.MediaType), model.MediaType);
            var Categories = Enum.GetName(typeof(WATemplateCategory), model.Category);
            var Language = Enum.GetName(typeof(EngagetoEntities.Enums.Language), model.LanguageCode);

            var Business = await _context.BusinessDetailsMetas.FirstOrDefaultAsync(t => t.BusinessId == model.BusinessId);
            var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == model.UserId);
            _client.DefaultRequestHeaders.Add("Authorization", "Bearer " + Business?.Token);

            if (MediaTypes != "TEXT" && MediaTypes != "NONE")
            {
                Handler = await GetHeaderHandle(model.Adapt<CreateTemplateDto>());
                HeaderHandle = Handler.Headerhandler;
                if (HeaderHandle == null)
                {
                    return null; // throw an exception
                }
            }
            string jsonData = "";
            List<string> cards = new List<string>();

            var mediaType = MediaExtentionsHelper.GetMediaTypeFromUrl(model.MediaFile ?? string.Empty);

            if (!Draft)
            {
                if (MediaTypes?.ToLower() == "carousel")
                {
                    jsonData = CreateTemplatePayloads.CarouselTemplatePayload(model.TemplateName, StringHelper.ExtractVariables(model.Body), model.LanguageCode.ToString(), model.Category.ToString(), model.Header ?? string.Empty, model.Body,
                    model.MediaType.ToString(), HeaderHandle ?? string.Empty, CarouselCardsPayload(model, HeaderHandle ?? string.Empty, mediaType), StringHelper.ExtractVariables(model.Body));
                }
                else
                {
                    throw new InvalidOperationException("Invalid Template as media type is not carousel!");

                }
                JObject json = JObject.Parse(jsonData);
                //Act for the None Template
                if (MediaTypes?.ToLower() == "none")
                {
                    json["components"]?.FirstOrDefault(c => c["type"]?.ToString() == "HEADER")?.Remove();
                }

                var (success, res) = await _metaApiService.CreateTemplateRequestAsync(Business?.BusinessId ?? string.Empty, json);
                if (success)
                {
                    response = new HttpResponseMessage(System.Net.HttpStatusCode.OK)
                    {
                        Content = new StringContent(res.ToString(), Encoding.UTF8, "application/json")
                    };
                }
                else
                {
                    response = new HttpResponseMessage(System.Net.HttpStatusCode.BadRequest)
                    {
                        Content = new StringContent(res.ToString(), Encoding.UTF8, "application/json")
                    };
                }

                if (response.IsSuccessStatusCode)
                {
                    await using var stream = await response.Content.ReadAsStreamAsync();
                    using var reader = new StreamReader(stream);
                    using var jsonReader = new JsonTextReader(reader);
                    var result = _serializer.Deserialize<EngagetoEntities.Entities.Template>(jsonReader);
                    WATemplateStatus status;
                    switch (result.Status)
                    {
                        case (WATemplateStatus)1:
                            status = WATemplateStatus.PENDING;
                            break;
                        case (WATemplateStatus)2:
                            status = WATemplateStatus.APPROVED;
                            break;
                        case (WATemplateStatus)3:
                            status = WATemplateStatus.REJECTED;
                            break;
                        default:
                            Console.WriteLine($"Unexpected status value: {result.Status}");
                            status = WATemplateStatus.PENDING; // Assign a default status
                            break;
                    }

                    if (DraftTemplate == null || !DraftTemplate.Any())
                    {
                        var templateEntity1 = new EngagetoEntities.Entities.Template
                        {
                            MetaId = result.MetaId,
                            Category = result.Category,
                            SubCategory = model.SubCategory,
                            TemplateName = model.TemplateName,
                            LanguageCode = model.LanguageCode.ToString(),
                            MediaType = model.MediaType,
                            MediaAwsUrl = Handler.S3Url,
                            Header = headerMessage,
                            Body = bodyMessage,
                            Status = status,
                            BusinessId = Business?.BusinessId ?? string.Empty,
                            UserId = model.UserId,
                            Createdby = user?.Name,
                            CreatedDate = DateTime.UtcNow,
                            UpdatedDate = DateTime.UtcNow,
                            Updatedby = user.Name,
                            CarouselCards = model.CarouselCardsJson?.Select(card => JsonConvert.SerializeObject(card)).ToList()

                        };
                        var newTemplate = _context.Templates.Add(templateEntity1);
                        await _context.SaveChangesAsync();

                        await UpdateButtons(newTemplate.Entity.TemplateId, model);
                    }
                    else
                    {
                        // Update existing draft templates
                        foreach (var draft in DraftTemplate)
                        {
                            draft.TemplateId = (Guid)model.TemplateId;
                            draft.MetaId = result.MetaId;
                            draft.Category = result.Category;
                            draft.SubCategory = model.SubCategory;
                            draft.TemplateName = model.TemplateName;
                            draft.LanguageCode = model.LanguageCode.ToString();
                            draft.MediaType = model.MediaType;
                            draft.MediaAwsUrl = Handler.S3Url;
                            draft.Header = model.Header;
                            draft.Body = model.Body;
                            draft.Status = status;
                            draft.BusinessId = Business?.BusinessId ?? string.Empty;
                            draft.UserId = model.UserId;
                            draft.Createdby = user?.Name;
                            draft.CreatedDate = DateTime.UtcNow;
                            draft.Updatedby = user?.Name;
                            draft.UpdatedDate = DateTime.UtcNow;
                            draft.CarouselCards = model.CarouselCardsJson?.Select(card => JsonConvert.SerializeObject(card)).ToList();
                            if (model.CarouselCardsJson == null) { draft.CarouselCards = new List<string>(); }

                        }
                        _context.Templates.UpdateRange(DraftTemplate);
                        await _context.SaveChangesAsync();

                        Guid TemplateId = (Guid)model.TemplateId;

                        var existingButtons = _context.ButtonDetails.Where(b => b.TemplateId == TemplateId).ToList();
                        _context.ButtonDetails.RemoveRange(existingButtons);
                        await _context.SaveChangesAsync();

                        await UpdateButtons(TemplateId, model);
                    }
                }
            }
            else
            {
                // Save the entity as a draft
                string language = model.LanguageCode.ToString();
                if (DraftTemplate == null || !DraftTemplate.Any())
                {
                    var templateEntity = new EngagetoEntities.Entities.Template
                    {
                        Category = model.Category,
                        SubCategory = model.SubCategory,
                        TemplateName = model.TemplateName,
                        MediaType = model.MediaType,
                        LanguageCode = model.LanguageCode.ToString(),
                        MediaAwsUrl = Handler.S3Url,
                        Header = headerMessage,
                        Body = bodyMessage,
                        Status = WATemplateStatus.DRAFT,
                        BusinessId = Business.BusinessId ?? string.Empty,
                        UserId = model.UserId,
                        Createdby = user?.Name,
                        CreatedDate = DateTime.UtcNow,
                        UpdatedDate = DateTime.UtcNow,
                        Updatedby = user.Name,
                        CarouselCards = model.CarouselCardsJson?.Select(card => JsonConvert.SerializeObject(card)).ToList()
                    };
                    var newTemplate = _context.Templates.Add(templateEntity);
                    await _context.SaveChangesAsync();

                    Guid TemplateId1 = newTemplate.Entity.TemplateId;
                    await UpdateButtons(TemplateId1, model);
                }
                else
                // Update existing draft templates
                if (DraftTemplate != null || DraftTemplate.Any())
                {
                    foreach (var draft in DraftTemplate)
                    {
                        draft.TemplateId = (Guid)model.TemplateId;
                        draft.Category = model.Category;
                        draft.SubCategory = model.SubCategory;
                        draft.TemplateName = model.TemplateName;
                        draft.LanguageCode = model.LanguageCode.ToString();
                        draft.MediaType = model.MediaType;
                        draft.MediaAwsUrl = Handler.S3Url;
                        draft.Header = headerMessage;
                        draft.Body = bodyMessage;
                        draft.Status = WATemplateStatus.DRAFT;
                        draft.BusinessId = Business?.BusinessId ?? string.Empty;
                        draft.UserId = model.UserId;
                        draft.CreatedDate = DateTime.UtcNow;
                        draft.Createdby = user?.Name;
                        draft.Updatedby = user?.Name;
                        draft.UpdatedDate = DateTime.UtcNow;
                        draft.CarouselCards = model.CarouselCardsJson?.Select(card => JsonConvert.SerializeObject(card)).ToList();
                        if (model.CarouselCardsJson == null) { draft.CarouselCards = new List<string>(); }
                    }
                    _context.Templates.UpdateRange(DraftTemplate);
                    await _context.SaveChangesAsync();

                    Guid TemplateId = (Guid)model.TemplateId;

                    var existingButtons = _context.ButtonDetails.Where(b => b.TemplateId == TemplateId).ToList();
                    _context.ButtonDetails.RemoveRange(existingButtons);
                    await _context.SaveChangesAsync();

                    await UpdateButtons(TemplateId, model);
                }
            }
            return response;
        }
        public async Task<HttpResponseMessage> SendCarouselTemplateAsync(SendCarouselTemplateDto model)
        {
            try
            {

                await _conversationsService.VerifySubscriptionAndExpectedWalletBalanceAysc(model.BusinessId, 1);
                var business = await _context.BusinessDetailsMetas.FirstOrDefaultAsync(t => t.BusinessId == model.BusinessId);
                var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == model.UserId);
                var template = await _context.Templates.FirstOrDefaultAsync(t => t.TemplateId == model.TemplateId);

                // Load buttons immediately after template
                var buttons = await _context.ButtonDetails.Where(m => m.TemplateId == template.TemplateId).ToListAsync();

                var regex = StringHelper.GetVariableRegexs();
                if (business == null) throw new Exception("Business not found");
                if (template == null)
                    throw new Exception("TemplateId not found.");
                if (template.CarouselCardsJson == null)
                {
                    if (model.CarouselVariables == null || !model.CarouselVariables.Any())
                        throw new Exception("At least one carousel card is required.");


                    var firstCard = model.CarouselVariables.FirstOrDefault();
                    if (firstCard == null || string.IsNullOrEmpty(firstCard.MediaUrl))
                        throw new Exception("First card must contain a valid MediaUrl");
                }
                string existingBody = "";
                var body = StringHelper.FormateTemplateComponents(template.Body);
                var bodyReplaced = StringHelper.ReplaceAndExtractVariables(body).UpdatedMessage;
                if (template.Body != null && StringHelper.GetVariableRegexs().IsMatch(bodyReplaced))
                {
                    if (model.BodyVariableValues == null || model.BodyVariableValues.Length != Regex.Matches(bodyReplaced, @"\{\{\d+\}\}").Count)
                    {
                        throw new Exception("BodyVariableValues do not match the template variables.");
                    }
                    existingBody = template.Body;
                }

                HttpResponseMessage response = null;

                foreach (var phoneNumberId in model.Contact)
                {
                    if (phoneNumberId != null)
                    {
                        string To = phoneNumberId;

                        if (!string.IsNullOrEmpty(phoneNumberId))
                        {
                            // Use pre-loaded buttons to avoid DbContext access
                            var urlButtons = buttons.Where(b => b.ButtonType == "URL").ToList();
                            var phoneNumberButtons = buttons.FirstOrDefault(b => b.ButtonType == "PHONE_NUMBER");
                            var quickReplyButtons = buttons.Where(b => b.ButtonType == "QUICK_REPLY").ToList();

                            if (template == null)
                            {
                                return new HttpResponseMessage(HttpStatusCode.NotFound);
                            }

                            var firstCarousel = model?.CarouselVariables?.FirstOrDefault(c => !string.IsNullOrEmpty(c.MediaUrl));

                            string HeaderHandle = null;
                            if (!string.IsNullOrEmpty(existingBody))
                            {
                                var formateBody = StringHelper.FormateTemplateComponents(existingBody);
                                var leadratVarBody = StringHelper.ReplaceAndExtractVariables(formateBody).UpdatedMessage;
                                if (!regex.IsMatch(leadratVarBody))
                                {
                                    model.BodyVariableValues = null;
                                }
                            }
                            List<CarouselCardsDto> jsonCardsConverted = JsonCardsConverter(template.CarouselCardsJson ?? string.Empty, model.CarouselVariables);

                            var templateBody = "";
                            if (model.BodyVariableValues != null && model.BodyVariableValues.Length > 0)
                            {
                                var formateTemplateBody = StringHelper.FormateTemplateComponents(template.Body);
                                var leadratBody = StringHelper.ReplaceAndExtractVariables(formateTemplateBody).UpdatedMessage;
                                templateBody = StringHelper.ReplacePlaceholders(leadratBody, model.BodyVariableValues.ToList());
                            }
                            else
                            {
                                model.BodyVariableValues = model.BodyVariableValues;
                            }

                            string TempHeader = template.Header ?? string.Empty;
                            string TemptBody = templateBody;
                            string TemptButtonQuickReply = String.Join(",", urlButtons.Select(m => m.ButtonName));
                            string TemptButtonRedirectUrl = String.Join(",", urlButtons.Select(b => b.ButtonValue));
                            var TempCarouselVariables = model.CarouselVariables;

                            var leadratVariableUrl = StringHelper.ReplaceAndExtractVariables(TemptButtonRedirectUrl).UpdatedMessage;
                            if (TemptButtonRedirectUrl != null && regex.IsMatch(leadratVariableUrl))
                            {
                                if (model.CarouselVariables.Any(variables => variables.RedirectUrlVariableValues == null))
                                {
                                    throw new Exception("RedirectUrlVariableValues is required");
                                }
                            }


                            var carouselBody = "";
                            foreach (var card in jsonCardsConverted)
                            {
                                var formateCardBody = StringHelper.FormateTemplateComponents(card.Body);
                                var leadratCardVarBody = StringHelper.ReplaceAndExtractVariables(formateCardBody).UpdatedMessage;
                                if (regex.IsMatch(leadratCardVarBody) && model.CarouselVariables != null)
                                {
                                    carouselBody = string.Join(",", model.CarouselVariables.Select(card => string.Join(",", card.BodyCarouselVariableValues.Select(value => $@"{{ ""type"": ""text"", ""text"": ""{value}"" }}"))));
                                }
                            }
                            var carouselCardsPayload = await SendCarouselCardsPayload(jsonCardsConverted, template, model.CarouselVariables);
                            var jsonPayload = CreateTemplatePayloads.SendCarouselTemplatePayload(template.TemplateName, phoneNumberId, model?.BodyVariableValues?.ToList() ?? new(), template.LanguageCode, carouselCardsPayload, carouselBody);

                            JObject json = JObject.Parse(jsonPayload);

                            var (success, res) = await _metaApiService.SendCarouselTemplateAsync(business.BusinessId ?? string.Empty, json);

                            if (success)
                            {
                                response = new HttpResponseMessage(System.Net.HttpStatusCode.OK)
                                {
                                    Content = new StringContent(res.ToString(), Encoding.UTF8, "application/json")
                                };
                            }
                            else
                            {
                                response = new HttpResponseMessage(System.Net.HttpStatusCode.BadRequest)
                                {
                                    Content = new StringContent(res.ToString(), Encoding.UTF8, "application/json")
                                };
                            }

                            if (response.IsSuccessStatusCode)
                            {
                                await using var stream = await response.Content.ReadAsStreamAsync();
                                using var reader = new StreamReader(stream);
                                using var jsonReader = new JsonTextReader(reader);
                                var sendResponse = _serializer.Deserialize<SendMessageResponseDto>(jsonReader);

                                EngagetoEntities.Enums.SendStatus status;
                                switch (sendResponse.Messages[0].Status)
                                {
                                    case EngagetoEntities.Enums.SendStatus.Accepted:
                                        status = EngagetoEntities.Enums.SendStatus.Accepted;
                                        break;
                                    case EngagetoEntities.Enums.SendStatus.Rejected:
                                        status = EngagetoEntities.Enums.SendStatus.Rejected;
                                        break;
                                    default:
                                        // Log or debug the unexpected status value
                                        Console.WriteLine($"Unexpected status value: {sendResponse.Messages[0].Status}");

                                        status = EngagetoEntities.Enums.SendStatus.Accepted;
                                        break;
                                }


                                string TemptButtonRedirectUrls = "";    // https://www.google.com/a,https://www.google.com/b
                                TemptButtonRedirectUrls = StringHelper.ReplaceAndExtractVariables(TemptButtonRedirectUrl ?? string.Empty).UpdatedMessage;
                                var urlVariables = model.CarouselVariables.SelectMany(i => i.RedirectUrlVariableValues ?? Array.Empty<string>()).ToList();
                                var replacedUrls = StringHelper.ReplacePlaceholders(TemptButtonRedirectUrls, urlVariables);
                                // Save data to Templates table
                                string conversationCarouselCards = UpdatedCarouselCardsJson(jsonCardsConverted, model.CarouselVariables ?? new());

                                Guid parsedBusinessId = Guid.TryParse(model.BusinessId, out var tempGuid) ? tempGuid : Guid.Empty;
                                Guid contactId = _context.Contacts.FirstOrDefault(i => i.BusinessId == parsedBusinessId && (i.CountryCode + i.Contact).Replace("+", "") == To.Replace("+", ""))?.ContactId ?? Guid.Empty;

                                EngagetoEntities.Entities.Conversations sendTemplateEntity = new()
                                {
                                    Id = Guid.NewGuid(),
                                    TemplateMediaType = template.MediaType,
                                    TemplateMediaUrl = template.MediaAwsUrl,
                                    MessageType = MessageType.Template,
                                    CreatedAt = DateTime.UtcNow,
                                    TemplateBody = TemptBody,
                                    TemplateHeader = TempHeader,
                                    TemplateFooter = template.Footer,
                                    CallButtonName = phoneNumberButtons == null ? "" : phoneNumberButtons.ButtonName,
                                    PhoneNumber = phoneNumberButtons == null ? "" : phoneNumberButtons.ButtonValue,
                                    UrlButtonNames = String.Join(",", urlButtons.Select(m => m.ButtonName)),
                                    RedirectUrls = replacedUrls,
                                    QuickReplies = String.Join(",", quickReplyButtons.Select(m => m.ButtonValue)),
                                    CarouselCards = conversationCarouselCards,
                                    BusinessId = parsedBusinessId,
                                    ContactId = contactId
                                };
                                if (sendResponse.Contacts != null && sendResponse.Contacts.Count > 0 && sendResponse.Messages != null && sendResponse.Messages.Count > 0)
                                {
                                    sendTemplateEntity.To = To;
                                    sendTemplateEntity.From = model.BusinessId;
                                    sendTemplateEntity.WhatsAppMessageId = sendResponse.Messages[0].WhatsAppMessageId;
                                    sendTemplateEntity.Status = ConvStatus.sent;
                                }
                                try
                                {
                                    _context.Conversations.Add(sendTemplateEntity);
                                    await _context.SaveChangesAsync();
                                }
                                catch (Exception ex)
                                {
                                    throw new Exception(ex.ToString());
                                }
                            }
                        }
                    }
                }
                return response;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<CreateTemplateDto> GetTemplateByIdAsync(Guid id)
        {
            var template = _context.Templates.Where(t => t.TemplateId == id).ToList();
            var buttons = _context.ButtonDetails.Where(b => b.TemplateId == id).ToList();
            var CountryCode = _context.CountryDetails.Select(m => m.CountryCode);
            CreateTemplateDto templates = new CreateTemplateDto();
            templates = template.FirstOrDefault().Adapt<CreateTemplateDto>();
            return templates;
        }

        public async Task<HttpResponseMessage> SendAuthenticationTemplateAsync(SendAuthenticationTemplateDto model, string oneTimeOtp)
        {
            await _conversationsService.VerifySubscriptionAndExpectedWalletBalanceAysc(model.BusinessId, 1);
            var business = await _context.BusinessDetailsMetas.FirstOrDefaultAsync(t => t.BusinessId == model.BusinessId);
            var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == model.UserId);
            if (string.IsNullOrEmpty(business?.BusinessId) || user?.Id == Guid.Empty || !string.Equals(user?.CompanyId, model.BusinessId, StringComparison.OrdinalIgnoreCase))
                throw new Exception("Either Business or user not found or user does not have access to the specified business.");
            var template = _context.Templates.FirstOrDefault(t => t.BusinessId == model.BusinessId && t.TemplateId == model.TemplateId);
            if (template == null && template?.Category != EngagetoEntities.Enums.WATemplateCategory.AUTHENTICATION)
                throw new Exception("TemplateId not found or invalid category.");

            HttpResponseMessage response = null;
            var phoneNumber = model.Contact;
            var data = _context.Contacts;

            if (phoneNumber != null)
            {
                string To = phoneNumber;

                if (!string.IsNullOrEmpty(phoneNumber))
                {
                    template.Buttons = _context.ButtonDetails.Where(m => m.TemplateId == template.TemplateId).ToList();

                    if (template == null)
                    {
                        return new HttpResponseMessage(HttpStatusCode.NotFound);
                    }

                    //for save into database
                    string TempHeader = template.Header ?? string.Empty;
                    string TemptBody = template.Body;
                    var authTemplateData = JsonConvert.DeserializeObject<JObject>(template.AuthTemplatePreviewJson ?? string.Empty);
                    int codeExpirationMinute = (int)(authTemplateData?["CodeExpirationMinutes"] ?? string.Empty);
                    var regex = StringHelper.GetVariableRegexs();
                    string oneTimePassword = "";
                    if (!string.IsNullOrEmpty(oneTimeOtp))
                    {
                        oneTimePassword = oneTimeOtp;
                    }
                    else
                    {
                        oneTimePassword = oneTimeOtp;
                        Random random = new Random();
                        int sixDigitRandom = random.Next(100000, 1000000);
                        oneTimePassword = sixDigitRandom.ToString();
                    }
                    var bodyVariableCount = StringHelper.GetVariableCount(template.Body);

                    string jsonData = CreateTemplatePayloads.SendAuthenticationTemplatePayload(phoneNumber, template.TemplateName, bodyVariableCount,
                                   template.LanguageCode.ToString(), template.Category.ToString() ?? WATemplateCategory.None.ToString(), oneTimePassword);


                    JObject jsonObject = JObject.Parse(jsonData);
                    var (success, res) = await _metaApiService.SendCarouselTemplateAsync(business.BusinessId, jsonObject);

                    if (success)
                    {
                        response = new HttpResponseMessage(System.Net.HttpStatusCode.OK)
                        {
                            Content = new StringContent(res.ToString(), Encoding.UTF8, "application/json")
                        };
                    }
                    else
                    {
                        response = new HttpResponseMessage(System.Net.HttpStatusCode.BadRequest)
                        {
                            Content = new StringContent(res.ToString(), Encoding.UTF8, "application/json")
                        };
                    }
                    if (response.IsSuccessStatusCode)
                    {
                        await using var stream = await response.Content.ReadAsStreamAsync();
                        using var reader = new StreamReader(stream);
                        using var jsonReader = new JsonTextReader(reader);
                        var result = _serializer.Deserialize<SendMessageResponseDto>(jsonReader);

                        EngagetoEntities.Enums.SendStatus status;
                        switch (result?.Messages[0].Status)
                        {
                            case EngagetoEntities.Enums.SendStatus.Accepted:
                                status = EngagetoEntities.Enums.SendStatus.Accepted;
                                break;
                            case EngagetoEntities.Enums.SendStatus.Rejected:
                                status = EngagetoEntities.Enums.SendStatus.Rejected;
                                break;
                            default:
                                // Log or debug the unexpected status value
                                Console.WriteLine($"Unexpected status value: {result?.Messages[0].Status}");
                                status = EngagetoEntities.Enums.SendStatus.Accepted; // Assign a default status
                                break;
                        }
                        // Save data to Templates table
                        List<string> otp = new List<string>();
                        otp.Add(oneTimePassword);
                        template.Body = template.Body.Replace("*", "");
                        var replacedBody = StringHelper.ReplacePlaceholders(template.Body, otp ?? new());

                        Guid parsedBusinessId = Guid.TryParse(model.BusinessId, out var tempGuid) ? tempGuid : Guid.Empty;
                        Guid contactId = _context.Contacts.FirstOrDefault(i => i.BusinessId == tempGuid && (i.CountryCode + i.Contact).Replace("+", "") == To.Replace("+", ""))?.ContactId ?? Guid.Empty;

                        EngagetoEntities.Entities.Conversations sendTemplateEntity = new()
                        {
                            Id = Guid.NewGuid(),
                            TemplateMediaType = template.MediaType,
                            TemplateMediaUrl = template.MediaAwsUrl,
                            CreatedAt = DateTime.UtcNow,
                            TemplateBody = replacedBody,
                            TemplateHeader = TempHeader,
                            TemplateFooter = template.Footer,
                            UrlButtonNames = "Copy code",
                            RedirectUrls = oneTimePassword,
                            BusinessId = parsedBusinessId,
                            ContactId = contactId
                        };
                        if (result.Contacts != null && result.Contacts.Count > 0 && result.Messages != null && result.Messages.Count > 0)
                        {
                            sendTemplateEntity.To = To;
                            sendTemplateEntity.From = model.BusinessId;
                            sendTemplateEntity.WhatsAppMessageId = result.Messages[0].WhatsAppMessageId;
                            sendTemplateEntity.Status = ConvStatus.sent;
                        }
                        try
                        {
                            _context.Conversations.Add(sendTemplateEntity);
                            await _context.SaveChangesAsync();
                        }
                        catch (Exception ex)
                        {
                            throw new Exception(ex.ToString());
                        }
                    }
                }
            }
            return response;

        }

        private async Task<EngagetoEntities.Entities.Conversations> SaveConversationAsync(EngagetoEntities.Entities.Template template,
            SendMessageResponseDto response, string companyId, TemplateRequestDto requestDto, EngagetoEntities.Enums.SourceType source)
        {
            var countryCodeAndPhoneNumber = PhoneNumberHelper.GetCountryCodeAndPhoneNumber(requestDto.Contact, requestDto.CountryCode);
            string contact = string.Concat(countryCodeAndPhoneNumber.CountryCode, countryCodeAndPhoneNumber.NationalNumber).Replace("+", "");
            var firstResponse = response?.Messages.FirstOrDefault();
            ConvStatus status = ConvStatus.sent;
            switch (response?.Messages?.FirstOrDefault()?.Status ?? SendStatus.Rejected)
            {
                case SendStatus.Accepted:
                    status = ConvStatus.sent;
                    break;
                case SendStatus.Rejected:
                    status = ConvStatus.failed;
                    break;
                default:
                    status = ConvStatus.failed;
                    break;
            }
            //check status of conversation message
            //var result = await _inboxService.CheckConversationSatus(companyId, string.Concat(countryCodeAndPhoneNumber.CountryCode, countryCodeAndPhoneNumber.NationalNumber), CancellationToken.None);
            var bodyMatches = Regex.Matches(template.Body, @"\{\{(\d+)\}\}");
            var headerMatches = Regex.Matches(template.Header ?? "", @"\{\{(\d+)\}\}");

            var phoneNumberButtons = template.Buttons?.FirstOrDefault(b => b.ButtonType == "PHONE_NUMBER");
            var urlButtons = template.Buttons?.Where(b => b.ButtonType == "URL").ToList();
            var quickReplyButtons = template.Buttons?.Where(b => b.ButtonType == "QUICK_REPLY").ToList();

            var sendTemplateEntity = new EngagetoEntities.Entities.Conversations
            {
                Id = Guid.NewGuid(),
                TemplateMediaType = template.MediaType,
                TemplateMediaUrl = template.MediaAwsUrl,
                CreatedAt = DateTime.UtcNow,
                WhatsAppMessageId = firstResponse?.WhatsAppMessageId ?? string.Empty,
                From = companyId?.ToLowerInvariant() ?? string.Empty,
                To = contact,
                TemplateBody = StringHelper.FormateString(ReplaceMatches(template.Body, bodyMatches, requestDto.BodyVariableValues ?? new())),
                TemplateHeader = StringHelper.FormateString(ReplaceMatches(template.Header ?? "", headerMatches, (requestDto.HeaderValue ?? "").Split(',').ToList())),
                TemplateFooter = template.Footer,
                CallButtonName = phoneNumberButtons?.ButtonName ?? "",
                PhoneNumber = phoneNumberButtons?.ButtonValue ?? "",
                UrlButtonNames = string.Join(",", urlButtons.Select(m => m.ButtonName)),
                RedirectUrls = string.Join(",", urlButtons.Select(x => x.ButtonValue)),
                QuickReplies = string.Join(",", quickReplyButtons.Select(m => m.ButtonValue)),
                Status = status,
                MessageType = MessageType.Template,
                ReferenceId = template.TemplateId.ToString(),
                BusinessId = Guid.TryParse(companyId, out var tempGuid) ? tempGuid : Guid.Empty
            };

            Guid? assignedUserId = null;
            if (!string.IsNullOrEmpty(requestDto.UserNumber))
            {
                var user = await _context.Users
                    .FirstOrDefaultAsync(u => u.PhoneNumber == requestDto.UserNumber
                        && u.CompanyId == companyId
                        && u.Status == true);

                if (user != null)
                {
                    assignedUserId = user.Id;
                }
            }
            if (assignedUserId == null)
            {
                var users = await _context.Users
                            .Where(u => u.CompanyId == companyId && u.Status)
                            .Include(u => u.UserRoles) // Role is not mapped, so we can't include that
                            .ToListAsync();
                var ownerRole = await _context.Role.Where(r => r.Name.ToLower() == "Owner" && r.CompanyId == companyId).FirstOrDefaultAsync();

                if (ownerRole == null)
                    throw new Exception("Role 'Owner' not found");

                var owner = users.FirstOrDefault(u => u.RoleId == ownerRole.Id.ToString());

                if (owner == null)
                    throw new Exception("Business owner not found");

                assignedUserId = owner.Id;
            }
            var newOrExistContact = await _contactRepository.SaveContactNumber(countryCodeAndPhoneNumber.CountryCode, countryCodeAndPhoneNumber.NationalNumber, companyId, source, requestDto.Name, assignedUserId);

            var resultDto = new ConversationDto();

            try
            {
                resultDto = sendTemplateEntity.Adapt<ConversationDto>();
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex);
            }

            await _whatsAppBusinessNotificarion.RenderMessagesBySignalR(companyId?.ToString(), new List<ConversationDto> { resultDto });
            //updating the error message delay 5 second if message is not delivered.
            //await _scheduledOperation.RunTaskAsync(sendTemplateEntity.Id, 10, CancellationToken.None);

            sendTemplateEntity.ContactId = newOrExistContact.ContactId;
            _context.Conversations.Add(sendTemplateEntity);
            await _context.SaveChangesAsync();
            return sendTemplateEntity;
        }
        public static string UpdatedCarouselCardsJson(List<CarouselCardsDto> jsonCardsConverted, List<CarouselCardVariableDto> carouselCardVariables)
        {
            try
            {
                var updatedCards = jsonCardsConverted.Select((card, index) =>
                {
                    if (index < carouselCardVariables.Count)
                    {
                        var carouselVariable = carouselCardVariables[index];

                        if (!string.IsNullOrEmpty(card.Body) && carouselVariable.BodyCarouselVariableValues != null)
                        {
                            var leadratVar = StringHelper.ReplaceAndExtractVariables(card.Body).UpdatedMessage;
                            card.Body = StringHelper.ReplacePlaceholders(leadratVar, carouselVariable.BodyCarouselVariableValues.ToList());

                        }

                        if (card.RedirectUrl != null && carouselVariable.RedirectUrlVariableValues != null)
                        {
                            for (int i = 0; i < card.RedirectUrl.Count(); i++)
                            {
                                if (i < carouselVariable.RedirectUrlVariableValues.Count())
                                {
                                    var leadratUrlVar = StringHelper.ReplaceAndExtractVariables(card.RedirectUrl[i]).UpdatedMessage;
                                    card.RedirectUrl[i] = StringHelper.ReplacePlaceholders(leadratUrlVar, new List<string> { carouselVariable.RedirectUrlVariableValues[i] });
                                }
                            }
                        }

                        if (!string.IsNullOrEmpty(carouselVariable.MediaUrl))
                        {
                            card.HeaderMediaUrl = carouselVariable.MediaUrl;
                        }
                    }
                    return card;
                }).ToList();

                var jsonArray = JArray.FromObject(updatedCards);
                return jsonArray.ToString();
            }
            catch (Exception ex)
            {
                return $"Error processing carouselCards: {ex.Message}";
            }
        }
        private static string ReplaceMatches(string input, MatchCollection matches, List<string> values)
        {
            foreach (Match match in matches.Cast<Match>())
            {
                if (int.TryParse(match.Groups[1].Value, out int index) && index >= 1 && index <= values.Count)
                {
                    string pattern = $"{{{{{index}}}}}";
                    input = input.Replace(pattern, values[index - 1]);
                }
            }
            return input;
        }
        public static List<CarouselCardsDto> JsonCardsConverter(string carouselCardsJson, List<CarouselCardVariableDto>? carouselVariables)
        {
            if (string.IsNullOrEmpty(carouselCardsJson))
            {
                throw new ArgumentException("CarouselCardsJson cannot be null or empty.");
            }

            List<CarouselCardsDto> cards = new List<CarouselCardsDto>();
            try
            {
                var jsonArray = JsonConvert.DeserializeObject<List<string>>(carouselCardsJson);

                if (jsonArray != null)
                {
                    foreach (var jsonString in jsonArray)
                    {
                        var card = JsonConvert.DeserializeObject<CarouselCardsDto>(jsonString);
                        if (card != null)
                        {
                            cards.Add(card);
                        }
                    }
                }
                else
                {
                    cards = JsonConvert.DeserializeObject<List<CarouselCardsDto>>(carouselCardsJson) ?? new List<CarouselCardsDto>();
                }
            }
            catch (JsonSerializationException ex)
            {
                throw new ArgumentException("Error deserializing CarouselCardsJson. Ensure the JSON matches the expected format.", ex);
            }

            return cards;
        }

        public async Task<Contacts> SaveContactAsync(SendAuthTemplateDto authTemplateDto, string businessId)
        {
            Guid.TryParse(businessId, out Guid companyId);
            var phoneNumber = authTemplateDto.CountryCode != null ? string.Concat(authTemplateDto.CountryCode, authTemplateDto.MobileNumber) : authTemplateDto.MobileNumber;
            var countryCodeWithPhoneNumber = PhoneNumberHelper.GetCountryCodeAndPhoneNumber(phoneNumber);
            var existContacts = await _context.Contacts
                .FirstOrDefaultAsync(x => x.CountryCode.Replace("+", "") == countryCodeWithPhoneNumber.CountryCode.Replace("+", "")
                    && x.Contact == countryCodeWithPhoneNumber.NationalNumber && x.BusinessId == companyId);
            if (existContacts == null)
            {
                var contact = new Contacts()
                {
                    Contact = countryCodeWithPhoneNumber.NationalNumber,
                    CountryCode = countryCodeWithPhoneNumber.CountryCode.StartsWith("+") ? countryCodeWithPhoneNumber.CountryCode : $"+{countryCodeWithPhoneNumber.CountryCode}",
                    Name = authTemplateDto.Name == null ? "Unknown" : authTemplateDto.Name,
                    IsActive = true,
                    IsOptIn = EngagetoEntities.Enums.Is_OptIn.optin,
                    ChatStatus = EngagetoEntities.Enums.ChatStatus.New,
                    Source = EngagetoEntities.Enums.SourceType.LeadRat,
                    BusinessId = companyId,
                    ContactId = Guid.NewGuid(),
                    CreatedDate = DateTime.UtcNow,
                };
                await _context.Contacts.AddAsync(contact);
                await _context.SaveChangesAsync();
                existContacts = contact;
            }
            return existContacts;
        }

        public string CarouselCardsPayload(CarouselTemplateDto model, string HeaderHandle, string mediaUrlType)
        {
            if (model == null || model.CarouselCardsJson == null || !model.CarouselCardsJson.Any())
            {
                return string.Empty;
            }

            var cards = model.CarouselCardsJson.Select(card =>
            {
                var formateBody = StringHelper.FormateTemplateComponents(card.Body);
                var replacedBody = StringHelper.ReplaceAndExtractVariables(formateBody).UpdatedMessage;
                var bodyExample = StringHelper.ExtractVariables(replacedBody);
                var bodypayload = !string.IsNullOrEmpty(card.Body)
                  ? string.IsNullOrEmpty(bodyExample)
                  ? $@"{{ ""type"": ""BODY"",
                    ""text"": ""{replacedBody}"" }}"
                   : $@"{{ ""type"": ""BODY"",
                     ""text"": ""{replacedBody}"",
                     ""example"": {{
                                    ""body_text"": [
                                                      [{bodyExample}]
                                                   ]
                                  }}
                 }}"
              : string.Empty;

                var cardComponents = new List<string> { $@"{{ ""type"":""header"",
                                                              ""format"":""{card.MediaUrlType}"",
                                                              ""example"":{{ ""header_handle"": [""{HeaderHandle}"" ] }}
                                                           }}",
                                                           {bodypayload}
                                                      };

                var buttonsPayload = GenerateButtonPayloadForCard(card);
                if (!string.IsNullOrEmpty(buttonsPayload))
                {
                    cardComponents.Add(buttonsPayload);
                }

                return $@"{{ ""components"": [{string.Join(",", cardComponents)}] }}";
            }).ToList();

            return $@"{{
                 ""type"": ""carousel"",
                 ""cards"": [{string.Join(",", cards)}]
              }}";
        }

        private static string GenerateButtonPayloadForCard(CarouselCardsDto card)
        {
            if (card == null) return string.Empty;
            var buttonsArray = new List<JObject>();
            if (!string.IsNullOrWhiteSpace(card.CountryCode) && !string.IsNullOrWhiteSpace(card.PhoneNumber) && !string.IsNullOrWhiteSpace(card.CallButtonName))
            {
                var phoneNumber = card.CountryCode.TrimStart('+') + card.PhoneNumber;
                buttonsArray.Add(new JObject  { { "type", "PHONE_NUMBER" },
                                                { "text", card.CallButtonName },
                                                { "phone_number", phoneNumber }
                                              });
            }

            if (card.UrlButtonName?.Count > 0 && card.RedirectUrl?.Count == card.UrlButtonName.Count)
            {
                for (int i = 0; i < card.UrlButtonName.Count; i++)
                {
                    var url = card.RedirectUrl[i];
                    var buttonObject = new JObject {{ "type", "URL" },
                                                    { "text", card.UrlButtonName[i] },
                                                    { "url", url }
                                                   };

                    var replacedUrl = StringHelper.ReplaceAndExtractVariables(url).UpdatedMessage;
                    var extractedVariables = StringHelper.ExtractVariables(replacedUrl);
                    if (!string.IsNullOrWhiteSpace(extractedVariables))
                    {
                        buttonObject.Add("example", JArray.Parse($"[{extractedVariables}]"));
                    }

                    buttonsArray.Add(buttonObject);
                }
            }

            if (card.QuickReply?.Count > 0)
            {
                buttonsArray.AddRange(card.QuickReply.Select(quickReplyText => new JObject
                                                       { { "type", "QUICK_REPLY" },
                                                         { "text", quickReplyText }
                                                       }));
            }

            // Build final payload
            return buttonsArray.Count > 0
                ? new JObject
                {
            { "type", "BUTTONS" },
            { "buttons", JArray.FromObject(buttonsArray) }
                }.ToString(Newtonsoft.Json.Formatting.None)
                : string.Empty;
        }

        public async Task<string> SendCarouselCardsPayload(List<CarouselCardsDto> carouselCards, Template template, List<CarouselCardVariableDto> variables)
        {
            await _conversationsService.VerifySubscriptionAndExpectedWalletBalanceAysc(template.BusinessId, 1);
            var business = await _context.BusinessDetailsMetas.FirstOrDefaultAsync(t => t.BusinessId == template.BusinessId);
            var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == template.UserId);
            if (business == null || user == null)
            {
                throw new Exception("Business or User not found.");
            }

            var regex = StringHelper.GetVariableRegexs();
            if (carouselCards == null || !carouselCards.Any())
            {
                throw new Exception("Carousel cards not found!");
            }

            var mediaType = MediaExtentionsHelper.GetMediaTypeFromUrl(template.MediaAwsUrl ?? string.Empty);

            var cards = carouselCards.Select((card, index) =>
            {
                var cardVariables = variables.ElementAtOrDefault(index);
                var bodyParameters = !string.IsNullOrEmpty(card.Body) && cardVariables?.BodyCarouselVariableValues != null
                    ? cardVariables.BodyCarouselVariableValues.Select(value => $@"{{ ""type"": ""text"", ""text"": ""{value}"" }}").ToList()
                    : new List<string>();


                var formateBody = StringHelper.FormateTemplateComponents(card.Body);
                var leadratVarBody = StringHelper.ReplaceAndExtractVariables(formateBody).UpdatedMessage;
                var bodyPayload = regex.IsMatch(leadratVarBody) && bodyParameters.Any()
                    ? $@"{{ ""type"": ""body"",
                            ""parameters"": [{string.Join(",", bodyParameters)}]
                         }}"
                    : string.Empty;

                var headerPayload = !string.IsNullOrEmpty(template.MediaAwsUrl)
            ? $@"{{ ""type"": ""header"",
                     ""parameters"": [{{
                                        ""type"": ""{card.MediaUrlType?.ToString().ToLower()}"",
                                        ""{card.MediaUrlType?.ToString().ToLower()}"": {{ ""link"": ""{card.HeaderMediaUrl}"" }}
                                      }}]
                   }}"
            : string.Empty;


                var buttonsPayload = SendCarouselButtonPayload(card, cardVariables ?? new());

                var cardComponents = new List<string> { headerPayload, bodyPayload, buttonsPayload }
                    .Where(component => !string.IsNullOrEmpty(component))
                    .ToList();

                return $@"{{ ""card_index"": ""{index}"", ""components"": [{string.Join(",", cardComponents)}] }}";
            }).ToList();

            return $@"{{ ""type"": ""carousel"", ""cards"": [{string.Join(",", cards)}] }}";
        }


        private async Task UpdateButtons(Guid templateId, CarouselTemplateDto model)
        {
            if (model?.CarouselCardsJson == null || !model.CarouselCardsJson.Any())
            {
                return;
            }

            foreach (var card in model.CarouselCardsJson)
            {
                if (!string.IsNullOrEmpty(card.CallButtonName) &&
                    !string.IsNullOrEmpty(card.PhoneNumber) &&
                    !string.IsNullOrEmpty(card.CountryCode))
                {
                    string phoneNumber = card.CountryCode.TrimStart('+') + card.PhoneNumber;
                    _context.ButtonDetails.Add(new EngagetoEntities.Entities.Button
                    {
                        TemplateId = templateId,
                        ButtonType = "PHONE_NUMBER",
                        ButtonName = card.CallButtonName,
                        ButtonValue = phoneNumber
                    });
                }

                if (card?.UrlButtonName != null && card.UrlButtonName.Count > 0 &&
                    card.RedirectUrl != null && card.RedirectUrl.Count > 0)
                {
                    for (int i = 0; i < Math.Min(card.UrlButtonName.Count, card.RedirectUrl.Count); i++)
                    {
                        if (string.IsNullOrEmpty(card.UrlButtonName[i]) ||
                            string.IsNullOrEmpty(card.RedirectUrl[i]))
                        {
                            continue;
                        }

                        _context.ButtonDetails.Add(new EngagetoEntities.Entities.Button
                        {
                            TemplateId = templateId,
                            ButtonType = "URL",
                            ButtonName = card.UrlButtonName[i],
                            ButtonValue = card.RedirectUrl[i]
                        });
                    }
                }

                if (card?.QuickReply != null && card.QuickReply.Count > 0)
                {
                    foreach (var quickReply in card.QuickReply)
                    {
                        if (string.IsNullOrEmpty(quickReply)) continue;

                        _context.ButtonDetails.Add(new EngagetoEntities.Entities.Button
                        {
                            TemplateId = templateId,
                            ButtonType = "QUICK_REPLY",
                            ButtonValue = quickReply
                        });
                    }
                }
            }
            await _context.SaveChangesAsync();
        }

        private static string SendCarouselButtonPayload(CarouselCardsDto card, CarouselCardVariableDto variables)
        {
            if (card.UrlButtonName?.Count > 0 && card.RedirectUrl?.Count == card.UrlButtonName.Count)
            {
                var buttonsArray = card.UrlButtonName.Select((name, index) => new JObject
                {
                   { "type", "button" },
                   { "sub_type", "url" },
                   { "index", index.ToString() },
                   { "parameters", new JArray  { new JObject
                    {
                        { "type", "text" },
                        { "text", variables?.RedirectUrlVariableValues != null && variables.RedirectUrlVariableValues.Length > index
                            ? variables.RedirectUrlVariableValues[index]
                            : name
                        }
                    }
                }
            }
            }).ToArray();

                return string.Join(",", buttonsArray.Select(x => x.ToString(Newtonsoft.Json.Formatting.None)));
            }
            return string.Empty;
        }
    }
}